package ca.kai.tasks;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Optional;

import lombok.val;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class TicklerCategoryServiceTest {

  @Mock
  private TicklerCategoryRepository ticklerCategoryRepository;

  @InjectMocks
  private TicklerCategoryService ticklerCategoryService;

  @BeforeEach
  public void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  public void givenCategoryExists_whenGetCategoryById_thenCategoryIsReturned() {
    // Arrange
    val category = new TicklerCategory();
    category.setId(1);
    category.setDescription("Test Category");
    when(ticklerCategoryRepository.findById(1)).thenReturn(Optional.of(category));
    // Act
    val result = ticklerCategoryService.getCategoryById(1);
    // Assert
    assertNotNull(result);
    assertEquals(1, result.getId());
    assertEquals("Test Category", result.getDescription());
  }

  @Test
  public void givenCategoryDoesNotExist_whenGetCategoryById_thenNullIsReturned() {
    // Arrange
    when(ticklerCategoryRepository.findById(1)).thenReturn(Optional.empty());
    // Act
    val result = ticklerCategoryService.getCategoryById(1);
    // Assert
    assertNull(result);
  }

  @Test
  public void givenActiveCategories_whenFindCategoryIdByDescription_thenCategoryIdIsReturned() {
    // Arrange
    val category1 = new TicklerCategory();
    category1.setId(1);
    category1.setDescription("Category One");
    category1.setActive(true);
    val category2 = new TicklerCategory();
    category2.setId(2);
    category2.setDescription("Category Two");
    category2.setActive(true);
    when(ticklerCategoryRepository.findByActiveTrue())
        .thenReturn(Arrays.asList(category1, category2));
    // Act
    val result = ticklerCategoryService.findCategoryIdByDescription("Category One");
    // Assert
    assertNotNull(result);
    assertEquals(1, result);
  }

  @Test
  public void givenActiveCategoriesExist_whenFindCategoryIdByDescription_thenNullIsReturned() {
    // Arrange
    val category1 = new TicklerCategory();
    category1.setId(1);
    category1.setDescription("Category One");
    category1.setActive(true);
    val category2 = new TicklerCategory();
    category2.setId(2);
    category2.setDescription("Category Two");
    category2.setActive(true);
    when(ticklerCategoryRepository.findByActiveTrue())
        .thenReturn(Arrays.asList(category1, category2));
    // Act
    val result = ticklerCategoryService.findCategoryIdByDescription("Nonexistent Category");
    // Assert
    assertNull(result);
  }
}
