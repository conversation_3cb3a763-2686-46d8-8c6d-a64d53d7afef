package ca.kai.fhir.resource.dstu2;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

import ca.kai.integration.tiahealth.OAuth2Config;
import javax.servlet.http.HttpServletRequest;
import org.hl7.fhir.dstu2.model.CodeableConcept;
import org.hl7.fhir.dstu2.model.Coding;
import org.hl7.fhir.dstu2.model.Conformance;
import org.hl7.fhir.dstu2.model.Conformance.ConformanceRestComponent;
import org.hl7.fhir.dstu2.model.Conformance.ConformanceRestSecurityComponent;
import org.hl7.fhir.dstu2.model.Extension;
import org.hl7.fhir.dstu2.model.UriType;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.web.context.WebApplicationContext;

@PrepareForTest(ServerMetadataResourceProvider.class)
@RunWith(PowerMockRunner.class)
public class ServerMetadataResourceProviderTest {

	private static final String TEST_AUTHORIZE_URL = "Test Authorize URL";
	private static final String TEST_TOKEN_URL = "Test Token URL";
	private static final String TEST_MANAGE_URL = "Test Manage URL";
	private static final String TEST_INTROSPECT_URL = "Test Introspect URL";
	private static final String TEST_REVOKE_URL = "Test Revoke URL";
	private static final String TEST_REGISTER_URL = "Test Register URL";

	private static final String AUTHORIZE_URL = "authorize";
	private static final String TOKEN_URL = "token";
	private static final String MANAGE_URL = "manage";
	private static final String INTROSPECT_URL = "introspect";
	private static final String REVOKE_URL = "revoke";
	private static final String REGISTER_URL = "register";

	@Mock
	private WebApplicationContext applicationContext;
	@Mock
	HttpServletRequest request;

	@Test
	public void testgetServerConformance_success() throws Exception {
		Conformance conformance = new Conformance();
		conformance.addRest();
		OAuth2Config mockOAuth2Config = PowerMockito.mock(OAuth2Config.class);
		PowerMockito.whenNew(OAuth2Config.class).withAnyArguments().thenReturn(mockOAuth2Config);
		when(mockOAuth2Config.getAuthorizeUrl()).thenReturn(TEST_AUTHORIZE_URL);
		when(mockOAuth2Config.getTokenUrl()).thenReturn(TEST_TOKEN_URL);
		when(mockOAuth2Config.getManageUrl()).thenReturn(TEST_MANAGE_URL);
		when(mockOAuth2Config.getIntrospectUrl()).thenReturn(TEST_INTROSPECT_URL);
		when(mockOAuth2Config.getRevokeUrl()).thenReturn(TEST_REVOKE_URL);
		when(mockOAuth2Config.getRegisterUrl()).thenReturn(TEST_REGISTER_URL);
		when(request.getRequestURI()).thenReturn("");

		ServerMetadataResourceProvider provider = PowerMockito
				.spy(new ServerMetadataResourceProvider(applicationContext));
		PowerMockito.doReturn(conformance).when(provider, "getServerConformanceFromSuperClass", Mockito.any(),
				Mockito.any());

		Conformance serverConformance = provider.getServerConformance(request, null);

		assertEquals("WELL Health Technologies Corp", serverConformance.getPublisher());
		ConformanceRestComponent restComponent = serverConformance.getRest().get(0);
		ConformanceRestSecurityComponent security = restComponent.getSecurity();
		Extension oAuth2Config = security.getExtension().get(0);
		assertEquals("http://fhir-registry.smarthealthit.org/StructureDefinition/oauth-uris", oAuth2Config.getUrl());
		assertEquals(TEST_AUTHORIZE_URL,
				((UriType) oAuth2Config.getExtensionsByUrl(AUTHORIZE_URL).get(0).getValue()).getValue());
		assertEquals(TEST_TOKEN_URL,
				((UriType) oAuth2Config.getExtensionsByUrl(TOKEN_URL).get(0).getValue()).getValue());
		assertEquals(TEST_MANAGE_URL,
				((UriType) oAuth2Config.getExtensionsByUrl(MANAGE_URL).get(0).getValue()).getValue());
		assertEquals(TEST_INTROSPECT_URL,
				((UriType) oAuth2Config.getExtensionsByUrl(INTROSPECT_URL).get(0).getValue()).getValue());
		assertEquals(TEST_REVOKE_URL,
				((UriType) oAuth2Config.getExtensionsByUrl(REVOKE_URL).get(0).getValue()).getValue());
		assertEquals(TEST_REGISTER_URL,
				((UriType) oAuth2Config.getExtensionsByUrl(REGISTER_URL).get(0).getValue()).getValue());

		CodeableConcept oauthService = security.getService().get(0);
		assertEquals("OAuth2 using SMART-on-FHIR profile", oauthService.getText());
		Coding coding = oauthService.getCoding().get(0);
		assertEquals("http://hl7.org/fhir/restful-security-service", coding.getSystem());
		assertEquals("SMART-on-FHIR", coding.getCode());
		}

}
