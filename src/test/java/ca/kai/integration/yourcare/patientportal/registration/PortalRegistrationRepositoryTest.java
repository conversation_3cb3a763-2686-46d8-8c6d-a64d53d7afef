package ca.kai.integration.yourcare.patientportal.registration;

import static org.junit.Assert.assertEquals;

import ca.oscarpro.base.IntegrationTestBase;
import com.github.springtestdbunit.annotation.DatabaseOperation;
import com.github.springtestdbunit.annotation.DatabaseSetup;
import com.github.springtestdbunit.annotation.DatabaseTearDown;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;

@DataJpaTest
@DatabaseSetup(
    value = "RegisterPatientPortalRepositoryTest.xml"
)
@DatabaseTearDown(
    value = "RegisterPatientPortalRepositoryTest.xml",
		type = DatabaseOperation.DELETE_ALL
)
public class PortalRegistrationRepositoryTest extends IntegrationTestBase {
  @Autowired PortalRegistrationRepository repository;

  @Test
  public void testFindById_success() {
    PortalRegistration register = repository.findOne(1);
    assertEquals(new Integer(1), register.getId());
    assertEquals(22, register.getDemographicNumber());
  }
}
