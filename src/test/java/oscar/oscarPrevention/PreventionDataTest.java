package oscar.oscarPrevention;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

import ca.kai.datasharing.DataSharingService;
import health.apps.gateway.LinkStatus;
import health.apps.gateway.service.GWConfigurationService;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.val;
import lombok.var;
import org.apache.commons.lang.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockedStatic.Verification;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.stubbing.Answer;
import org.oscarehr.common.dao.DemographicExtDao;
import org.oscarehr.common.dao.PartialDateDao;
import org.oscarehr.common.dao.PreventionDao;
import org.oscarehr.common.dao.PreventionExtDao;
import org.oscarehr.common.dao.ProviderDataDao;
import org.oscarehr.common.enums.FeatureFlagEnum;
import org.oscarehr.common.model.Demographic;
import org.oscarehr.common.model.DemographicExt;
import org.oscarehr.common.model.DemographicExtKey;
import org.oscarehr.common.model.PartialDate;
import org.oscarehr.common.model.Prevention;
import org.oscarehr.common.model.PreventionExt;
import org.oscarehr.managers.DHIRSubmissionManager;
import org.oscarehr.managers.DemographicManager;
import org.oscarehr.managers.adapter.DemographicAdapter;
import org.oscarehr.util.LoggedInInfo;
import org.oscarehr.util.SpringUtils;
import oscar.util.UtilDateUtilities;

@ExtendWith(MockitoExtension.class)
@Execution(ExecutionMode.SAME_THREAD)
public class PreventionDataTest {

  private static final int DEMOGRAPHIC_NUMBER = 1;
  private static final Calendar BIRTHDAY = Calendar.getInstance();
  private static final String SEX = "M";
  private static final int REMOTE_SYSTEM_ID = 22;
  private static final String PROVIDER_NUMBER = "999998";
  private static final String LOCATION = "Test Location";
  private static final String DATE = "2025";
  private static final String PROVIDER_NAME = "Provider Name";

  // Preventions
  private static final String PREVENTION_TYPE_A = "TypeA";
  private static final String GUID_A = "aaaa9748-23bf-49f6-8203-5fdc000e7545";
  private static final String PREVENTION_ID_A = "1";
  private static final String PREVENTION_TYPE_B = "TypeB";
  private static final String GUID_B = "bbbb31cd-881e-41f2-889d-e80b97169a7c";

  @Mock
  private DemographicManager demographicManager;
  @Mock
  private PreventionDao preventionDao;
  @Mock
  private PreventionExtDao preventionExtDao;
  @Mock
  private LoggedInInfo loggedInInfo;
  @Mock
  private GWConfigurationService gwConfigurationService;
  @Mock
  private DemographicExtDao demographicExtDao;
  @Mock
  private ProviderDataDao providerDataDao;
  @Mock
  private PartialDateDao partialDateDao;
  @Mock
  private DataSharingService dataSharingService;

  private static MockedStatic<SpringUtils> springUtils;

  @BeforeEach
  public void setUp() {
    springUtils = mockStatic(SpringUtils.class);
    springUtils.when(SpringUtils.getBean(DemographicManager.class))
        .thenReturn(demographicManager);

    springUtils.when((Verification) SpringUtils.getBean("preventionDaoImpl"))
        .thenReturn(preventionDao);
    springUtils.when(SpringUtils.getBean(GWConfigurationService.class))
        .thenReturn(gwConfigurationService);
    springUtils.when((Verification) SpringUtils.getBean("preventionExtDao"))
        .thenReturn(preventionExtDao);
    springUtils.when((Verification) SpringUtils.getBean("preventionDao"))
        .thenReturn(preventionDao);
    springUtils.when(SpringUtils.getBean(ProviderDataDao.class)).thenReturn(providerDataDao);
    springUtils.when(SpringUtils.getBean(PartialDateDao.class)).thenReturn(partialDateDao);
    springUtils.when(SpringUtils.getBean(DataSharingService.class)).thenReturn(dataSharingService);

    // assign mocks to service
    DemographicAdapter.assignStaticDaos(
        null,
        null,
        null,
        demographicExtDao,
        null,
        null,
        null,
        null,
        null
    );
  }

  @AfterEach
  public void tearDown() {
    springUtils.close();
  }

  @Test
  public void givenLinkedDemographic_whenGetPrevention_thenReturnLocalAndRemotePreventions() {
    when(demographicManager.getDemographic(any(), anyInt())).thenReturn(createTestDemographic());
    mockDemographicLinkStatus(LinkStatus.LINKED.name());
    mockGatewayEnabled(true);

    // Mock preventions
    when(preventionDao.findActiveByDemoId(anyInt()))
        .thenReturn(createTestLocalPreventions(PREVENTION_TYPE_A, GUID_A));
    when(preventionDao.fetchRemotePreventions(anyInt()))
        .thenReturn(createTestRemotePreventions(PREVENTION_TYPE_B, GUID_B));

    val prevention = PreventionData.getPrevention(loggedInInfo, DEMOGRAPHIC_NUMBER);

    val preventions = prevention.preventionTypes;
    assertEquals(2, preventions.size());

    val aTypePreventions = prevention.getPreventionData(PREVENTION_TYPE_A);
    assertEquals(1, aTypePreventions.size());
    assertFalse(isRemotePreventionItem(aTypePreventions.get(0)));

    val bTypePreventions = prevention.getPreventionData(PREVENTION_TYPE_B);
    assertEquals(1, bTypePreventions.size());
    assertTrue(isRemotePreventionItem(bTypePreventions.get(0)));

    verifyRemotePreventionsAreFetched();
  }

  @Test
  public void givenLinkedDemographicAndSamePreventionType_whenGetPrevention_thenReturnLocalAndRemotePreventions() {
    when(demographicManager.getDemographic(any(), anyInt())).thenReturn(createTestDemographic());
    mockDemographicLinkStatus(LinkStatus.LINKED.name());
    mockGatewayEnabled(true);

    // Mock preventions
    when(preventionDao.findActiveByDemoId(anyInt()))
        .thenReturn(createTestLocalPreventions(PREVENTION_TYPE_A, GUID_A));
    when(preventionDao.fetchRemotePreventions(anyInt()))
        .thenReturn(createTestRemotePreventions(PREVENTION_TYPE_A, GUID_B));

    val prevention = PreventionData.getPrevention(loggedInInfo, DEMOGRAPHIC_NUMBER);

    val preventions = prevention.preventionTypes;
    assertEquals(1, preventions.size());

    val aTypePreventions = prevention.getPreventionData(PREVENTION_TYPE_A);
    assertEquals(2, aTypePreventions.size());
    assertFalse(isRemotePreventionItem(aTypePreventions.get(0)));
    assertTrue(isRemotePreventionItem(aTypePreventions.get(1)));

    verifyRemotePreventionsAreFetched();
  }

  @Test
  public void givenLinkedDemographicAndDuplicatePreventions_whenGetPrevention_thenReturnLocalPrevention() {
    when(demographicManager.getDemographic(any(), anyInt())).thenReturn(createTestDemographic());
    mockDemographicLinkStatus(LinkStatus.LINKED.name());
    mockGatewayEnabled(true);

    // Mock preventions
    when(preventionDao.findActiveByDemoId(anyInt()))
        .thenReturn(createTestLocalPreventions(PREVENTION_TYPE_A, GUID_A));
    when(preventionDao.fetchRemotePreventions(anyInt()))
        .thenReturn(createTestRemotePreventions(PREVENTION_TYPE_A, GUID_A));

    val prevention = PreventionData.getPrevention(loggedInInfo, DEMOGRAPHIC_NUMBER);

    val preventions = prevention.preventionTypes;
    assertEquals(1, preventions.size());

    val aTypePreventions = prevention.getPreventionData(PREVENTION_TYPE_A);
    assertEquals(1, aTypePreventions.size());
    assertFalse(isRemotePreventionItem(aTypePreventions.get(0)));

    verifyRemotePreventionsAreFetched();
  }

  @Test
  public void givenUnlinkedDemographic_whenGetPrevention_thenReturnLocalPreventions() {
    when(demographicManager.getDemographic(any(), anyInt())).thenReturn(createTestDemographic());
    mockDemographicLinkStatus(null);

    // Mock preventions
    when(preventionDao.findActiveByDemoId(anyInt()))
        .thenReturn(createTestLocalPreventions(PREVENTION_TYPE_A, GUID_A));

    val prevention = PreventionData.getPrevention(loggedInInfo, DEMOGRAPHIC_NUMBER);

    val preventions = prevention.preventionTypes;
    assertEquals(1, preventions.size());

    val aTypePreventions = prevention.getPreventionData(PREVENTION_TYPE_A);
    assertEquals(1, aTypePreventions.size());
    assertFalse(isRemotePreventionItem(aTypePreventions.get(0)));

    verifyRemotePreventionsAreNotFetched();
  }

  @Test
  public void givenGatewayDisabled_whenGetPrevention_thenReturnLocalPreventions() {
    when(demographicManager.getDemographic(any(), anyInt())).thenReturn(createTestDemographic());
    mockDemographicLinkStatus(LinkStatus.LINKED.name());
    mockGatewayEnabled(false);

    // Mock preventions
    when(preventionDao.findActiveByDemoId(anyInt()))
        .thenReturn(createTestLocalPreventions(PREVENTION_TYPE_A, GUID_A));

    val prevention = PreventionData.getPrevention(loggedInInfo, DEMOGRAPHIC_NUMBER);

    val preventions = prevention.preventionTypes;
    assertEquals(1, preventions.size());

    val aTypePreventions = prevention.getPreventionData(PREVENTION_TYPE_A);
    assertEquals(1, aTypePreventions.size());
    assertFalse(isRemotePreventionItem(aTypePreventions.get(0)));

    verifyRemotePreventionsAreNotFetched();
  }

  private void verifyRemotePreventionsAreFetched() {
    verify(preventionDao).fetchRemotePreventions(DEMOGRAPHIC_NUMBER);
  }

  private void verifyRemotePreventionsAreNotFetched() {
    verify(preventionDao, never()).fetchRemotePreventions(DEMOGRAPHIC_NUMBER);
  }

  private void mockDemographicLinkStatus(final String linkStatus) {
    val extension = new DemographicExt();
    extension.setValue(linkStatus);
    lenient().when(demographicExtDao.getDemographicExt(anyInt(), eq(DemographicExtKey.LINK_STATUS)))
        .thenReturn(extension);
  }

  private void mockGatewayEnabled(final boolean enabled) {
    lenient().when(gwConfigurationService.isLinkEnabled()).thenReturn(enabled);
  }

  private List<Prevention> createTestLocalPreventions(final String preventionType,
      final String guid) {
    return createTestPreventions(preventionType, guid, false);
  }

  private List<Prevention> createTestRemotePreventions(final String preventionType,
      final String guid) {
    return createTestPreventions(preventionType, guid, true);
  }

  private List<Prevention> createTestPreventions(final String preventionType, final String guid,
      final boolean isRemote) {
    return Collections.singletonList(createTestPrevention(preventionType, guid, isRemote));
  }

  private Prevention createTestPrevention(final String preventionType, final String guid,
      final boolean isRemote) {
    val prevention = new Prevention();
    prevention.setId(1);
    prevention.setPreventionType(preventionType);
    if (guid != null) {
      prevention.setGuid(guid);
    }
    if (isRemote) {
      prevention.setRemoteSystemId(REMOTE_SYSTEM_ID);
    }
    return prevention;
  }

  private Demographic createTestDemographic() {
    val demographic = new Demographic();
    demographic.setDemographicNo(DEMOGRAPHIC_NUMBER);
    demographic.setBirthDay(BIRTHDAY);
    demographic.setSex(SEX);
    return demographic;
  }

  private boolean isRemotePreventionItem(final Object preventionItem) {
    return ((PreventionItem) preventionItem).isRemoteEntry();
  }

  /**
   * Helper method used to set private static field value.
   * Primarily used to set PreventionData's dao fields.
   *
   * @param clazz Class to set field value
   * @param fieldName Field name to set value
   * @param value Value to set
   * @throws NoSuchFieldException
   * @throws IllegalAccessException
   */
  private void setPrivateStaticField(Class<?> clazz, String fieldName, Object value)
      throws NoSuchFieldException, IllegalAccessException {
    Field field = clazz.getDeclaredField(fieldName);
    field.setAccessible(true);
    field.set(null, value);
  }

  @Test
  public void givenPreventionIsRemote_whenGetPreventionById_thenResultContainsExpectedValue()
      throws NoSuchFieldException, IllegalAccessException {

    val dhirSubmissionmanager = mock(DHIRSubmissionManager.class);
    springUtils.when(SpringUtils.getBean(DHIRSubmissionManager.class)).thenReturn(dhirSubmissionmanager);
    val prevention = new Prevention();
    prevention.setId(1);
    prevention.setDemographicId(2);
    setPrivateStaticField(PreventionData.class, "preventionDao", preventionDao);
    when(preventionDao.find(1)).thenReturn(prevention);

    val result = PreventionData.getPreventionById("1");
    assertEquals("false", result.get("isRemote"));

    prevention.setRemoteSystemId(3);
    val remoteResult = PreventionData.getPreventionById("1");
    assertEquals("true", remoteResult.get("isRemote"));
  }

  @Test
  public void givenPreventionIsRemote_whenGetPreventionData_thenResultContainsExpectedValue()
      throws NoSuchFieldException, IllegalAccessException {

    val prevention = new Prevention();
    prevention.setId(1);
    prevention.setDemographicId(2);
    List<Prevention> preventions = new ArrayList<>();
    preventions.add(prevention);
    when(preventionDao.findNotDeletedByDemographicId(1)).thenReturn(preventions);
    setPrivateStaticField(PreventionData.class, "preventionDao", preventionDao);

    when(gwConfigurationService.isLinkFeatureEnabled(FeatureFlagEnum.PREVENTIONS_ENABLED)).thenReturn(true);

    val result = PreventionData.getPreventionData(null, null, 1);
    assertEquals(1, result.size());
    assertFalse((boolean) result.get(0).get("isRemote"));

    prevention.setRemoteSystemId(3);
    val remoteResult = PreventionData.getPreventionData(null, null, 1);
    assertEquals(1, remoteResult.size());
    assertTrue((boolean) remoteResult.get(0).get("isRemote"));
  }

  @Test
  public void givenInvalidPreventionId_whenDeletePreventionData_thenDoNothing()
      throws NoSuchFieldException, IllegalAccessException {
    when(preventionDao.find(any())).thenReturn(null);
    setPrivateStaticField(PreventionData.class, "preventionDao", preventionDao);
    PreventionData.deletePreventionData("0");
    verify(preventionDao, never()).merge(any());
    verify(preventionDao, never()).saveToPrimary(any(), any());
  }

  @Test
  public void givenPrevention_whenDeletePreventionData_thenMergeAndSavePreventionToPrimary() throws NoSuchFieldException, IllegalAccessException {
    when(preventionDao.find(any()))
        .thenReturn(createTestPrevention(PREVENTION_TYPE_A, GUID_A, true));
    when(demographicManager.getDemographic(any())).thenReturn(createTestDemographic());
    doNothing().when(preventionDao).merge(any());
    doNothing().when(preventionDao).saveToPrimary(any(), any());
    setPrivateStaticField(PreventionData.class, "preventionDao", preventionDao);
    setPrivateStaticField(PreventionData.class, "demographicManager", demographicManager);

    PreventionData.deletePreventionData(PREVENTION_ID_A);

    val localPreventionCaptor = ArgumentCaptor.forClass(Prevention.class);
    verify(preventionDao).merge(localPreventionCaptor.capture());
    val savedLocalPrevention = localPreventionCaptor.getValue();
    assertEquals(PREVENTION_TYPE_A, savedLocalPrevention.getPreventionType());
    assertNotNull(savedLocalPrevention.getGuid());
    assertEquals(GUID_A, savedLocalPrevention.getGuid());
    assertTrue(savedLocalPrevention.isDeleted());

    val remotePreventionCaptor = ArgumentCaptor.forClass(Prevention.class);
    verify(preventionDao).saveToPrimary(remotePreventionCaptor.capture(), any());
    val savedRemotePrevention = remotePreventionCaptor.getValue();
    assertEquals(PREVENTION_TYPE_A, savedRemotePrevention.getPreventionType());
    assertNotNull(savedRemotePrevention.getGuid());
    assertEquals(GUID_A, savedRemotePrevention.getGuid());
    assertTrue(savedRemotePrevention.isDeleted());
  }

  @Test
  public void givenLocalAndRemovePreventions_whenConvertPreventiosToMap_thenPreventionsAreSorted()
      throws NoSuchFieldException, IllegalAccessException {
    val prev1 = createTestPrevention(PREVENTION_TYPE_A, GUID_A, true);
    prev1.setId(1);
    prev1.setPreventionDate(UtilDateUtilities.StringToDate("2024-11-01", "yyyy-MM-dd"));

    val prev2 = createTestPrevention(PREVENTION_TYPE_A, GUID_B, true);
    prev2.setId(2);
    prev2.setPreventionDate(UtilDateUtilities.StringToDate("2024-11-02", "yyyy-MM-dd"));

    List<Prevention> linkPreventions = new ArrayList<>();
    linkPreventions.add(prev2);
    linkPreventions.add(prev1);

    val localPrevention = createTestPrevention(PREVENTION_TYPE_A, null, false);
    localPrevention.setId(3);
    localPrevention.setPreventionDate(
        UtilDateUtilities.StringToDate("2024-11-03", "yyyy-MM-dd"));
    when(preventionDao.findNotDeletedByDemographicId(1))
        .thenReturn(Collections.singletonList(localPrevention));
    when(partialDateDao.getDatePartial(anyString(), anyInt(), anyInt(), anyInt())).thenReturn(null);
    setPrivateStaticField(PreventionData.class, "preventionDao", preventionDao);
    setPrivateStaticField(PreventionData.class, "partialDateDao", partialDateDao);
    val preventionData = PreventionData.getPreventionData(null, null, 1);

    val result = PreventionData.
        convertPreventionsToMap(preventionData, linkPreventions, PREVENTION_TYPE_A, null);
    assertEquals(3, result.size());
    assertTrue((boolean) result.get(0).get("isRemote"));
    assertEquals("1", result.get(0).get("id"));
    assertTrue((boolean) result.get(1).get("isRemote"));
    assertEquals("2", result.get(1).get("id"));
    assertFalse((boolean) result.get(2).get("isRemote"));
    assertEquals("3", result.get(2).get("id"));
  }

  @Test
  public void givenPreventionFeatureFlag_whenGetPreventionData_thenResultsFilterRemotePreventions()
      throws NoSuchFieldException, IllegalAccessException {

    List<Prevention> preventions = new ArrayList<>();
    preventions.add(createTestPrevention("preventionType", "localPrevention", false));
    preventions.add(createTestPrevention("preventionType", "remotePrevention", true));
    when(preventionDao.findByTypeAndDemoNo("preventionType", 1)).thenReturn(preventions);
    setPrivateStaticField(PreventionData.class, "preventionDao", preventionDao);

    when(gwConfigurationService.isLinkFeatureEnabled(FeatureFlagEnum.PREVENTIONS_ENABLED)).thenReturn(true);
    var actualPreventions = PreventionData.getPreventionData(null, "preventionType", 1);
    assertEquals(2, actualPreventions.size());
    assertFalse((Boolean) actualPreventions.get(0).get("isRemote"));
    assertTrue((Boolean) actualPreventions.get(1).get("isRemote"));

    when(gwConfigurationService.isLinkFeatureEnabled(FeatureFlagEnum.PREVENTIONS_ENABLED)).thenReturn(false);
    actualPreventions = PreventionData.getPreventionData(null, "preventionType", 1);
    assertEquals(1, actualPreventions.size());
    assertFalse((Boolean) actualPreventions.get(0).get("isRemote"));
  }

  @Test
  public void givenPreventionData_whenInsertPreventionData_thenPersistAndSavePreventionToPrimary()
      throws NoSuchFieldException, IllegalAccessException {
    setPrivateStaticFieldsForInsertPreventionData();
    mockPreventionDaoPersist();
    when(demographicManager.getDemographic(any())).thenReturn(createTestDemographic());
    val result = createTestSubjectForInsertPreventionData("0");
    verify(preventionDao, times(1)).persist(any(Prevention.class));
    verify(preventionDao, times(1)).saveToPrimary(any(Prevention.class), any(Demographic.class));
    assertEquals(123, result);
  }

  @Test
  public void givenPreventionFailure_whenInsertPreventionData_thenReturnFallbackId()
      throws NoSuchFieldException, IllegalAccessException {
    setPrivateStaticFieldsForInsertPreventionData();
    val result = createTestSubjectForInsertPreventionData("0");
    verify(preventionDao, times(1)).persist(any(Prevention.class));
    verify(preventionDao, never()).saveToPrimary(any(Prevention.class), any(Demographic.class));
    assertEquals(-1, result);
  }

  @Test
  public void givenRefusedStatusOne_whenInsertPreventionData_thenSetStatusToRefused()
      throws NoSuchFieldException, IllegalAccessException {
    setPrivateStaticFieldsForInsertPreventionData();
    val result = createTestSubjectForInsertPreventionData("1");
    val localPreventionCaptor = ArgumentCaptor.forClass(Prevention.class);
    verify(preventionDao, times(1)).persist(localPreventionCaptor.capture());
    val savedLocalPrevention = localPreventionCaptor.getValue();
    verify(preventionDao, never()).saveToPrimary(any(Prevention.class), any(Demographic.class));
    assertTrue(savedLocalPrevention.isRefused());
    assertEquals(-1, result);
  }

  @Test
  public void givenRefusedStatusTwo_whenInsertPreventionData_thenSetStatusToIneligible()
      throws NoSuchFieldException, IllegalAccessException {
    setPrivateStaticFieldsForInsertPreventionData();
    val result = createTestSubjectForInsertPreventionData("2");
    val localPreventionCaptor = ArgumentCaptor.forClass(Prevention.class);
    verify(preventionDao, times(1)).persist(localPreventionCaptor.capture());
    val savedLocalPrevention = localPreventionCaptor.getValue();
    verify(preventionDao, never()).saveToPrimary(any(Prevention.class), any(Demographic.class));
    assertTrue(savedLocalPrevention.isIneligible());
    assertEquals(-1, result);
  }

  @Test
  public void givenRefusedStatusThree_whenInsertPreventionData_thenSetStatusToRefused()
      throws NoSuchFieldException, IllegalAccessException {
    setPrivateStaticFieldsForInsertPreventionData();
    val result = createTestSubjectForInsertPreventionData("3");
    val localPreventionCaptor = ArgumentCaptor.forClass(Prevention.class);
    verify(preventionDao, times(1)).persist(localPreventionCaptor.capture());
    val savedLocalPrevention = localPreventionCaptor.getValue();
    verify(preventionDao, never()).saveToPrimary(any(Prevention.class), any(Demographic.class));
    assertTrue(savedLocalPrevention.isCompletedExternally());
    assertEquals(-1, result);
  }

  @Test
  public void givenPartialDateForPrevention_whenInsertPreventionData_thenPersistPartialDate()
      throws NoSuchFieldException, IllegalAccessException {
    setPrivateStaticFieldsForInsertPreventionData();
    val result = createTestSubjectForInsertPreventionData("0");
    verify(partialDateDao, times(1)).persist(any(PartialDate.class));
    assertEquals(-1, result);
  }

  @Test
  public void givenValidParameters_whenUpdatetPreventionData_thenUpdatePrevention()
      throws NoSuchFieldException, IllegalAccessException {
    val prevention = new Prevention();
    prevention.setId(1);
    mockPreventionDaoPersist();
    when(preventionDao.find(1)).thenReturn(prevention);
    setPrivateStaticField(PreventionData.class, "preventionDao", preventionDao);
    createTestSubjectForUpdatePreventionData();
    verify(preventionDao, times(1)).merge(any(Prevention.class));
    verify(preventionDao, times(1)).persist(any(Prevention.class));
    verify(preventionDao, times(2)).saveToPrimary(any(Prevention.class), any());
  }

  private void mockPreventionDaoPersist() {
    doAnswer(new Answer() {
      @Override
      public Object answer(InvocationOnMock invocation) {
        Object[] args = invocation.getArguments();
        ((Prevention) args[0]).setId(123);
        return args[0];
      }
    }).when(preventionDao).persist(any(Prevention.class));
  }

  private void setPrivateStaticFieldsForInsertPreventionData()
      throws NoSuchFieldException, IllegalAccessException {
    setPrivateStaticField(PreventionData.class, "preventionDao", preventionDao);
    setPrivateStaticField(PreventionData.class, "demographicManager", demographicManager);
    setPrivateStaticField(PreventionData.class, "partialDateDao", partialDateDao);
  }

  private Integer createTestSubjectForInsertPreventionData(String refused) {
    return PreventionData.insertPreventionData(
        PROVIDER_NUMBER,
        PREVENTION_ID_A,
        DATE,
        PROVIDER_NUMBER,
        LOCATION,
        PROVIDER_NAME,
        PREVENTION_TYPE_A,
        refused,
        StringUtils.EMPTY,
        StringUtils.EMPTY,
        new ArrayList<>(),
        PREVENTION_ID_A,
        null
    );
  }

  private void createTestSubjectForUpdatePreventionData() {
    PreventionData.updatetPreventionData(
        PREVENTION_ID_A,
        PROVIDER_NUMBER,
        PREVENTION_ID_A,
        DATE,
        PROVIDER_NUMBER,
        LOCATION,
        PROVIDER_NAME,
        PREVENTION_TYPE_A,
        StringUtils.EMPTY,
        StringUtils.EMPTY,
        StringUtils.EMPTY,
        new ArrayList<>(),
        PREVENTION_ID_A);
  }


  /*
   * Show that when multiple preventions exist for a demographic with the same preventionType that
   * the output of getAllPreventionData returns a Map with key "preventionTypeA" and value a list
   * of the preventions sorted by prevention date.
   */
  @Test
  public void givenSinglePreventionType_whenGetAllPreventionData_thenOutputSortsPreventionsIntoOneGroup()
      throws NoSuchFieldException, IllegalAccessException {

    List<Prevention> preventions = new ArrayList<>();
    preventions.add(createTestPrevention("preventionTypeA", "prevention1", false));
    preventions.add(createTestPrevention("preventionTypeA", "prevention2", false));
    preventions.add(createTestPrevention("preventionTypeA", "prevention3", false));
    preventions.get(0).setPreventionDate(UtilDateUtilities.StringToDate("2024-11-02", "yyyy-MM-dd"));
    preventions.get(1).setPreventionDate(UtilDateUtilities.StringToDate("2024-11-01", "yyyy-MM-dd"));
    preventions.get(2).setPreventionDate(UtilDateUtilities.StringToDate("2024-11-03", "yyyy-MM-dd"));

    when(preventionDao.findNotDeletedByDemographicId(1)).thenReturn(preventions);
    setPrivateStaticField(PreventionData.class, "preventionDao", preventionDao);

    val preventionsMap = PreventionData.getAllPreventionData(null, 1);
    assertEquals(1, preventionsMap.size());
    assertEquals(3, preventionsMap.get("preventionTypeA").size());
    assertEquals("prevention2", preventionsMap.get("preventionTypeA").get(0).get("guid"));
    assertEquals("prevention1", preventionsMap.get("preventionTypeA").get(1).get("guid"));
    assertEquals("prevention3", preventionsMap.get("preventionTypeA").get(2).get("guid"));
  }

  /*
   * Show that when multiple preventions exist for a demographic with different prevention types,
   * the output of getAllPreventionData returns a Map with keys "preventionTypeA" and
   * "preventionTypeB" and the test preventions are categorized and sorted correctly.
   */
  @Test
  public void givenMultiplePreventionTypes_whenGetAllPreventionData_thenOutputSortsPreventionsByType()
      throws NoSuchFieldException, IllegalAccessException {

    List<Prevention> preventions = new ArrayList<>();
    preventions.add(createTestPrevention("preventionTypeA", "prevention1", false));
    preventions.add(createTestPrevention("preventionTypeB", "prevention2", false));
    preventions.add(createTestPrevention("preventionTypeA", "prevention3", false));
    preventions.get(0).setPreventionDate(UtilDateUtilities.StringToDate("2024-11-02", "yyyy-MM-dd"));
    preventions.get(1).setPreventionDate(UtilDateUtilities.StringToDate("2024-11-01", "yyyy-MM-dd"));
    preventions.get(2).setPreventionDate(UtilDateUtilities.StringToDate("2024-11-03", "yyyy-MM-dd"));

    when(preventionDao.findNotDeletedByDemographicId(1)).thenReturn(preventions);
    setPrivateStaticField(PreventionData.class, "preventionDao", preventionDao);

    val preventionsMap = PreventionData.getAllPreventionData(null, 1);
    assertEquals(2, preventionsMap.size());
    assertEquals(2, preventionsMap.get("preventionTypeA").size());
    assertEquals("prevention1", preventionsMap.get("preventionTypeA").get(0).get("guid"));
    assertEquals("prevention3", preventionsMap.get("preventionTypeA").get(1).get("guid"));

    assertEquals(1, preventionsMap.get("preventionTypeB").size());
    assertEquals("prevention2", preventionsMap.get("preventionTypeB").get(0).get("guid"));

  }

  /*
   * Show that as long as preventionDao.findByTypeAndDemoNo's output is a subset of
   * preventionDao.findNotDeletedByDemographicId, then the output of getPreventionData is the same
   * as the output of getAllPreventionData, differing only by the outer map returned by
   * getAllPreventionData.
   */
  @Test
  public void givenComparingGetPreventionDataTo_whenGetAllPreventionData_thenOutputIsEqual()
      throws NoSuchFieldException, IllegalAccessException {

    List<Prevention> preventions = new ArrayList<>();
    preventions.add(createTestPrevention("preventionTypeA", "prevention1", false));
    preventions.add(createTestPrevention("preventionTypeA", "prevention2", false));
    preventions.add(createTestPrevention("preventionTypeA", "prevention3", false));
    preventions.get(0).setPreventionDate(UtilDateUtilities.StringToDate("2024-11-02", "yyyy-MM-dd"));
    preventions.get(1).setPreventionDate(UtilDateUtilities.StringToDate("2024-11-01", "yyyy-MM-dd"));
    preventions.get(2).setPreventionDate(UtilDateUtilities.StringToDate("2024-11-03", "yyyy-MM-dd"));

    when(preventionDao.findNotDeletedByDemographicId(1)).thenReturn(preventions);
    when(preventionDao.findByTypeAndDemoNo("preventionTypeA", 1)).thenReturn(preventions);
    setPrivateStaticField(PreventionData.class, "preventionDao", preventionDao);

    val preventionsMap = PreventionData.getAllPreventionData(null, 1);
    val preventionsList = PreventionData.getPreventionData(null, "preventionTypeA", 1);

    assertEquals(1, preventionsMap.size());
    assertEquals(3, preventionsMap.get("preventionTypeA").size());
    assertEquals(preventionsList, preventionsMap.get("preventionTypeA"));

  }

  private PreventionExt mockPreventionExtension(int preventionId, String keyval, String val) {
    val preventionExt = new PreventionExt();
    preventionExt.setPreventionId(preventionId);
    preventionExt.setKeyval(keyval);
    preventionExt.setVal(val);
    return preventionExt;
  }

  /*
   * Show that when multiple preventionExt records exist with the same preventionId, then the
   * output of getPreventionExtensionDataForDemographic returns a map with key "1" and value a map
   * with keys and values specified by the preventionExt records.
   */
  @Test
  public void givenMultiplePreventionExtensionsForOnePrevention_whenGetPreventionExtensionDataForDemographic_thenResultContainsExtensionValues()
      throws NoSuchFieldException, IllegalAccessException {

    List<PreventionExt> preventionExtensions = new ArrayList<>();
    preventionExtensions.add(mockPreventionExtension(1, "key1", "val1"));
    preventionExtensions.add(mockPreventionExtension(1, "key2", "val2"));

    when(preventionExtDao.findByDemographicId(1)).thenReturn(preventionExtensions);
    setPrivateStaticField(PreventionData.class, "preventionExtDao", preventionExtDao);

    val result = PreventionData.getPreventionExtensionDataForDemographic(1);
    val expectedResult = new HashMap<String, Map<String, String>>();
    expectedResult.put("1", new HashMap<String, String>(){{
      put("key1", "val1"); put("key2", "val2");}});
    assertEquals(expectedResult, result);
  }

  /*
   * Show that when multiple preventionExt records exist with different preventionIds, then the output
   * of getPreventionExtensionDataForDemographic returns a map with keys for each preventionId and
   * corresponding values are maps for preventionExt records' keys and values.
   */
  @Test
  public void givenMultiplePreventionExtensionsForMultiplePreventions_whenGetPreventionExtensionDataForDemographic_thenResultContainsTwoEntries()
      throws NoSuchFieldException, IllegalAccessException {

    List<PreventionExt> preventionExtensions = new ArrayList<>();
    preventionExtensions.add(mockPreventionExtension(1, "key1", "val1"));
    preventionExtensions.add(mockPreventionExtension(1, "key2", "val2"));
    preventionExtensions.add(mockPreventionExtension(2, "key3", "val3"));
    preventionExtensions.add(mockPreventionExtension(2, "key4", "val4"));

    when(preventionExtDao.findByDemographicId(1)).thenReturn(preventionExtensions);
    setPrivateStaticField(PreventionData.class, "preventionExtDao", preventionExtDao);

    val result = PreventionData.getPreventionExtensionDataForDemographic(1);
    val expectedResult = new HashMap<String, Map<String, String>>();
    expectedResult.put("1", new HashMap<String, String>(){{
      put("key1", "val1"); put("key2", "val2");}});
    expectedResult.put("2", new HashMap<String, String>(){{
      put("key3", "val3"); put("key4", "val4");}});
    assertEquals(expectedResult, result);
  }

  /*
   * Show that when the output of preventionExtDao.findByPreventionId is a subset of
   * preventionExtDao.findByDemographicId, then the output of getPreventionExtensionDataForDemographic
   * is the same as the output of getPreventionKeyValues, excluding the outer map.
   */
  @Test
  public void givenComparingGetPreventionKeyValuesTo_whenGetPreventionKeyValues_thenOutputIsEqual()
      throws NoSuchFieldException, IllegalAccessException {

    List<PreventionExt> preventionExtensions = new ArrayList<>();
    preventionExtensions.add(mockPreventionExtension(1, "key1", "val1"));
    preventionExtensions.add(mockPreventionExtension(1, "key2", "val2"));

    when(preventionExtDao.findByDemographicId(999)).thenReturn(preventionExtensions);
    when(preventionExtDao.findByPreventionId(1)).thenReturn(preventionExtensions);
    setPrivateStaticField(PreventionData.class, "preventionExtDao", preventionExtDao);

    val actualResult = PreventionData.getPreventionExtensionDataForDemographic(999);
    val expectedResult = PreventionData.getPreventionKeyValues("1");

    assertEquals(expectedResult, actualResult.get("1"));
  }

}

@ExtendWith(MockitoExtension.class)
class PreventionDataGetPreventionKeyValuesTest {

  @Mock private PreventionExtDao preventionExtDao;
  private MockedStatic<SpringUtils> springUtils;

  @BeforeEach
  public void setUp() {
    springUtils = mockStatic(SpringUtils.class);
    springUtils.when((Verification) SpringUtils.getBean("preventionExtDao"))
        .thenReturn(preventionExtDao);
  }

  @AfterEach
  public void tearDown() {
    springUtils.close();
  }

  @Test
  public void givenRemotePrevention_whenGetPreventionKeyValues_thenUseTransientPreventionExtList() {
    val prevention = new Prevention();
    prevention.setRemoteSystemId(1);
    val preventionExt = new PreventionExt();
    preventionExt.setKeyval("Transient Key");
    preventionExt.setVal("Transient Value");
    prevention.getPreventionExts().add(preventionExt);

    val keyValues = PreventionData.getPreventionKeyValues(prevention);

    assertEquals(1, keyValues.size());
    assertEquals("Transient Value", keyValues.get("Transient Key"));
  }

  private void setPrivateStaticField(Class<?> clazz, String fieldName, Object value)
      throws NoSuchFieldException, IllegalAccessException {
    Field field = clazz.getDeclaredField(fieldName);
    field.setAccessible(true);
    field.set(null, value);
  }

  @Test
  public void givenLocalPrevention_whenGetPreventionKeyValues_thenUseDatabasePreventionExtList()
      throws NoSuchFieldException, IllegalAccessException {
    val prevention = new Prevention();
    prevention.setId(1);
    val preventionExt = new PreventionExt();
    preventionExt.setKeyval("Database Key");
    preventionExt.setVal("Database Value");

    setPrivateStaticField(PreventionData.class, "preventionExtDao", preventionExtDao);
    when(preventionExtDao.findByPreventionId(prevention.getId())).thenReturn(
        Collections.singletonList(preventionExt));

    val keyValues = PreventionData.getPreventionKeyValues(prevention);

    assertEquals(1, keyValues.size());
    assertEquals("Database Value", keyValues.get("Database Key"));
  }

  @Test
  public void givenRemotePreventionFromGateway_whenGetPreventionExtMap_thenGetExtsFromGatewayPrevention() {
    Map<String,Object> preventionDataMap = new HashMap<>();
    Map<String, Map<String, String>> localPreventionExts = new HashMap<>();
    List<Prevention> linkPreventions = new ArrayList<>();

    // Simulate remote prevention
    preventionDataMap.put("isRemote", true);
    preventionDataMap.put("guid", "GUID");

    // Simulate matching gateway prevention
    Prevention gatewayPrevention = createTestPrevention(1, "GUID", true);
    linkPreventions.add(gatewayPrevention);

    Map<String, String> preventionExtMap = PreventionData.getPreventionExtMap(
        preventionDataMap, localPreventionExts, linkPreventions);

    assertEquals(1, preventionExtMap.size());
    assertEquals("val1", preventionExtMap.get("key1"));
  }

  @Test
  public void givenRemotePreventionFromDatabase_whenGetPreventionExtMap_thenGetExtsFromDatabasePrevention() {
    Map<String,Object> preventionDataMap = new HashMap<>();
    Map<String, Map<String, String>> localPreventionExts = new HashMap<>();
    List<Prevention> linkPreventions = new ArrayList<>();

    // Simulate remote prevention
    preventionDataMap.put("isRemote", true);
    preventionDataMap.put("guid", "GUID");
    preventionDataMap.put("id", "2");

    // Simulate matching database prevention
    addTestLocalPreventionExt(localPreventionExts, 2);

    Map<String, String> preventionExtMap = PreventionData.getPreventionExtMap(
        preventionDataMap, localPreventionExts, linkPreventions);

    assertEquals(1, preventionExtMap.size());
    assertEquals("val2", preventionExtMap.get("key2"));
  }

  @Test
  public void givenLocalPreventionFromDatabase_whenGetPreventionExtMap_thenGetExtsFromDatabasePrevention() {
    Map<String,Object> preventionDataMap = new HashMap<>();
    Map<String, Map<String, String>> localPreventionExts = new HashMap<>();
    List<Prevention> linkPreventions = new ArrayList<>();

    // Simulate local prevention
    preventionDataMap.put("isRemote", false);
    preventionDataMap.put("guid", "GUID");
    preventionDataMap.put("id", "3");

    // Simulate matching database prevention
    addTestLocalPreventionExt(localPreventionExts, 3);

    Map<String, String> preventionExtMap = PreventionData.getPreventionExtMap(
        preventionDataMap, localPreventionExts, linkPreventions);

    assertEquals(1, preventionExtMap.size());
    assertEquals("val2", preventionExtMap.get("key2"));
  }

  @Test
  public void givenNotFoundPreventionFromDatabase_whenGetPreventionExtMap_thenReturnEmptyMap() {
    Map<String,Object> preventionDataMap = new HashMap<>();
    Map<String, Map<String, String>> localPreventionExts = new HashMap<>();
    List<Prevention> linkPreventions = new ArrayList<>();

    // Simulate missing prevention
    preventionDataMap.put("isRemote", false);
    preventionDataMap.put("guid", "GUID");
    preventionDataMap.put("id", "3");

    // Simulate non-matching gateway prevention
    Prevention gatewayPrevention = createTestPrevention(1, "NON MATCHING GUID", true);
    linkPreventions.add(gatewayPrevention);

    // Simulate non-matching database prevention
    addTestLocalPreventionExt(localPreventionExts, 4);

    Map<String, String> preventionExtMap = PreventionData.getPreventionExtMap(
        preventionDataMap, localPreventionExts, linkPreventions);

    assertNotNull(preventionExtMap);
    assertTrue(preventionExtMap.isEmpty());
  }

  private void addTestLocalPreventionExt(
      final Map<String, Map<String, String>> localPreventionExts, final Integer id) {
    val extMap = new HashMap<String, String>();
    extMap.put("key2", "val2");
    localPreventionExts.put(id.toString(), extMap);
  }

  private Prevention createTestPrevention(final Integer id, final String guid,
      final boolean isRemote) {
    val prevention = new Prevention();
    prevention.setId(id);
    prevention.setGuid(guid);
    prevention.setRemoteSystemId(isRemote ? 1 : null);
    prevention.addPreventionExt("key1", "val1");
    return prevention;
  }
}
