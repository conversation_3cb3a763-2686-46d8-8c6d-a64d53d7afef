/**
 * Copyright (c) 2023 WELL EMR Group Inc. This software is made available under the terms of the GNU
 * General Public License, Version 2, 1991 (GPLv2). License details are available via
 * "gnu.org/licenses/gpl-2.0.html".
 */
package oscar.oscarBilling.ca.on.data;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.stream.Stream;
import lombok.val;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.oscarehr.common.dao.ProfessionalSpecialistDao;
import org.oscarehr.common.model.ProfessionalSpecialist;
import org.oscarehr.common.model.SystemPreferences;
import org.oscarehr.util.SpringUtils;
import oscar.util.SystemPreferencesUtils;

@ExtendWith(MockitoExtension.class)
public class JdbcBillingPageUtilTest {

  public static final String MONTHS_LIMIT = "6";
  @Mock
  private ProfessionalSpecialistDao professionalSpecialistDao;

  private MockedStatic<SpringUtils> springUtils;
  private MockedStatic<SystemPreferencesUtils> systemPreferencesUtilsMockedStatic;
  private MockedStatic<LocalDate> localDateMockedStatic;

  @BeforeEach
  public void init() {
    springUtils = mockStatic(SpringUtils.class);
    springUtils
        .when(() -> SpringUtils.getBean(eq("professionalSpecialistDao")))
        .thenReturn(professionalSpecialistDao);
    systemPreferencesUtilsMockedStatic = mockStatic(SystemPreferencesUtils.class);

    // Mock LocalDate.now() to return a fixed date to avoid month-end edge cases
    localDateMockedStatic = mockStatic(LocalDate.class);
    LocalDate fixedDate = LocalDate.of(2025, 6, 15); // Use June 15, 2025 as reference
    localDateMockedStatic.when(LocalDate::now).thenReturn(fixedDate);
  }

  @AfterEach
  public void after() {
    springUtils.close();
    if (systemPreferencesUtilsMockedStatic != null) {
      systemPreferencesUtilsMockedStatic.close();
    }
    if (localDateMockedStatic != null) {
      localDateMockedStatic.close();
    }
  }

  @Test
  public void givenNullSpecialist_whenGetReferralDocSpecialityType_thenReturnEmptyString() {
    when(professionalSpecialistDao.getByReferralNo("1")).thenReturn(null);
    val jdbcBillingPageUtil = spy(new JdbcBillingPageUtil());
    val result = jdbcBillingPageUtil.getReferralDoctorSpecialityType("1");
    Assertions.assertEquals("", result);
  }

  @Test
  public void givenNullSpecialityType_whenGetReferralDocSpecialityType_thenReturnEmptyString() {
    val specialist = createSpecialistWithType(null);
    when(professionalSpecialistDao.getByReferralNo("1")).thenReturn(specialist);
    val jdbcBillingPageUtil = spy(new JdbcBillingPageUtil());
    val result = jdbcBillingPageUtil.getReferralDoctorSpecialityType("1");
    Assertions.assertEquals("", result);
  }

  @Test
  public void givenEmptySpecialityType_whenGetReferralDocSpecialityType_thenReturnEmptyString() {
    val specialist = createSpecialistWithType("");
    when(professionalSpecialistDao.getByReferralNo("1")).thenReturn(specialist);
    val jdbcBillingPageUtil = spy(new JdbcBillingPageUtil());
    val result = jdbcBillingPageUtil.getReferralDoctorSpecialityType("1");
    Assertions.assertEquals("", result);
  }

  @Test
  public void givenSpecialityType_whenGetReferralDocSpecialityType_thenReturnSpecialistType() {
    val specialist = createSpecialistWithType("specialist");
    when(professionalSpecialistDao.getByReferralNo("1")).thenReturn(specialist);
    val jdbcBillingPageUtil = spy(new JdbcBillingPageUtil());
    val result = jdbcBillingPageUtil.getReferralDoctorSpecialityType("1");
    Assertions.assertEquals("specialist", result);
  }

  @ParameterizedTest
  @MethodSource("undoLimitTestData")
  public void givenPreferenceAndFileDate_whenCheckUndoLimit_thenCorrectResult(
      final String preferenceValue,
      final int monthsAgo,
      final int daysAgo,
      final boolean expected
  ) {
    systemPreferencesUtilsMockedStatic.when(() ->
        SystemPreferencesUtils.getPreferenceValueByName(
            SystemPreferences.CLAIM_FILE_UNDO_LIMIT_MONTHS, MONTHS_LIMIT)
    ).thenReturn(preferenceValue);

    val util = new JdbcBillingPageUtil();
    val dateToTest = Date.from(
        LocalDate.now().minusMonths(monthsAgo).minusDays(daysAgo)
            .atStartOfDay(ZoneId.systemDefault()).toInstant());
    Assertions.assertEquals(expected, util.isWithinUndoLimit(dateToTest));
  }

  private static Stream<org.junit.jupiter.params.provider.Arguments> undoLimitTestData() {
    return Stream.of(
        // Null check
        org.junit.jupiter.params.provider.Arguments.of(MONTHS_LIMIT, 0, 0, true),
        // Valid preference scenarios
        org.junit.jupiter.params.provider.Arguments.of(MONTHS_LIMIT, 5, 29, true),
        org.junit.jupiter.params.provider.Arguments.of(MONTHS_LIMIT, 5, 30, true),
        org.junit.jupiter.params.provider.Arguments.of(MONTHS_LIMIT, 6, 0, true),
        org.junit.jupiter.params.provider.Arguments.of(MONTHS_LIMIT, 6, 1, false),
        org.junit.jupiter.params.provider.Arguments.of(MONTHS_LIMIT, 6, 2, false),
        org.junit.jupiter.params.provider.Arguments.of(MONTHS_LIMIT, 7, 2, false),
        // Invalid preference fallback (default = 6)
        org.junit.jupiter.params.provider.Arguments.of("INVALID", 5, 29, true),
        org.junit.jupiter.params.provider.Arguments.of("INVALID", 5, 30, true),
        org.junit.jupiter.params.provider.Arguments.of("INVALID", 6, 0, true),
        org.junit.jupiter.params.provider.Arguments.of("INVALID", 6, 1, false),
        org.junit.jupiter.params.provider.Arguments.of("INVALID", 6, 2, false),
        org.junit.jupiter.params.provider.Arguments.of("INVALID", 7, 2, false)
    );
  }

  @Test
  public void givenNullDate_whenIsWithinUndoLimit_thenReturnFalse() {
    val util = new JdbcBillingPageUtil();
    val result = util.isWithinUndoLimit(null);
    Assertions.assertFalse(result);
  }

  private ProfessionalSpecialist createSpecialistWithType(final String specialityType) {
    val specialist = new ProfessionalSpecialist();
    specialist.setSpecialtyType(specialityType);
    return specialist;
  }
}
