package com.well.migrator;

import org.flywaydb.core.api.MigrationInfo;
import org.flywaydb.core.api.MigrationVersion;
import org.flywaydb.core.api.logging.Log;
import org.flywaydb.core.Flyway;
import org.flywaydb.core.api.FlywayException;
import org.flywaydb.core.api.MigrationInfoService;
import org.flywaydb.core.api.logging.LogFactory;
import org.flywaydb.core.api.output.InfoResult;
import org.flywaydb.core.internal.info.MigrationInfoDumper;
import picocli.CommandLine;
import picocli.CommandLine.*;

@Command(sortOptions = false)
public class FirstDataBank implements Runnable {

    private static Log logger = LogFactory.getLog(FirstDataBank.class);

    private static final String MIGRATION_TABLE = "FDBMigrations";
    private static final String MIGRATION_BASELINE_DESCRIPTION = "FirstDataBank Init";
    private static final String MIGRATION_PACKAGE = "classpath:db/migration";
    private static final String DATABASE_OPTIONS
        = "&zeroDateTimeBehavior=round"
            + "&useOldAliasMetadataBehavior=true"
            + "&jdbcCompliantTruncation=false";

    @Option(
        order = 0,
        names = {"-u", "--user"},
        defaultValue = "mysql",
        description = "database user name"
    )
    String user;

    @Option(
        order = 1,
        names = {"-p", "--pass"},
        description = "database user password",
        defaultValue = "mysql"
    )
    String password;

    @Option(
        order = 2,
        names = {"-d", "--db"},
        description = "database schema",
        defaultValue = "fdb"
    )
    String schema;

    @Option(
        order = 3,
        names = {"-h", "--host"},
        description = "database server host or ip",
        defaultValue = "localhost"
    )
    String host;
    @Option(
        order = 4,
        names = {"-r", "--port"},
        description = "database server port",
        defaultValue = "3306"
    )
    Integer port;

    @Option(
        order = 5,
        names = {"-l", "--location"},
        description = "location for additional migrations",
        defaultValue = MIGRATION_PACKAGE
    )
    String location;

    @Option(
        order = 6,
        names = {"--clean"},
        description = "drop all objects",
        defaultValue = "false"
    )
    Boolean clean;

    @Option(
        order = 7,
        names = {"--info"},
        description = "prints detail and status information",
        defaultValue = "false"
    )
    Boolean info;

    @Option(
        order = 8,
        names = {"--repair"},
        description = "repair schema history table",
        defaultValue = "false"
    )
    Boolean repair;


    @Option(
        order = 9,
        names = {"--undo"},
        description = "undoes the most recently applied migration",
        defaultValue = "false",
        hidden = true // Requires 3k USD a year to activate
    )
    Boolean undo;

    public static void main(String[] args) {
        int exitCode = new CommandLine(new FirstDataBank()).execute(args);
        System.exit(exitCode);
    }

    @Override
    public void run() {
        printOptions();
        Flyway flyway = configureFlyway();
        if (info) {
            printInfo(flyway);
        } else if (clean) {
            flyway.clean();
        } else if (repair) {
            flyway.repair();
        } else if (undo) {
            flyway.undo();
        } else {
            migrate(flyway);
        }
    }

    private Flyway configureFlyway() {
        String url = "jdbc:mysql://" + host + ":" + port + "/" + "?" + DATABASE_OPTIONS;
        return Flyway.configure()
                .dataSource(url, user, password)
                .placeholderPrefix("|||FLYWAY_REPLACE|||}")
                .table(MIGRATION_TABLE)
                .baselineDescription(MIGRATION_BASELINE_DESCRIPTION)
                .locations(location.split(","))
                .createSchemas(true)
                .schemas(schema)
                .load();
    }

    private void migrate(Flyway flyway) {
        try {
            flyway.migrate();
        } catch (FlywayException e) {
            if (e.getMessage().startsWith("Found non-empty schema")) {
                try {
                    flyway.baseline();
                    flyway.migrate();
                } catch (FlywayException e2) {
                    e2.printStackTrace();
                    System.exit(1);
                }
            } else {
                e.printStackTrace();
                System.exit(1);
            }
        }
    }

    private void printInfo(Flyway flyway) {
        MigrationInfoService info = flyway.info();
        MigrationInfo current = info.current();
        MigrationVersion currentSchemaVersion = current == null ? MigrationVersion.EMPTY : current.getVersion();
        MigrationVersion schemaVersionToOutput = currentSchemaVersion == null ? MigrationVersion.EMPTY : currentSchemaVersion;
        logger.info("Schema version: " + schemaVersionToOutput);
        logger.info("");
        InfoResult result = info.getInfoResult();
        MigrationInfo[] infos = info.all();
        logger.info("\n" + MigrationInfoDumper.dumpToAsciiTable(infos));
    }

    private void printOptions() {
        logger.info("\n"
                + "User   : " + user + "\n"
                + "Pass   : " + password + "\n"
                + "Schema : " + schema + "\n"
                + "Host   : " + host + "\n"
                + "Port   : " + port + "\n"
        );
    }
}
