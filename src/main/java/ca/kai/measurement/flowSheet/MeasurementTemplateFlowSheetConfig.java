package ca.kai.measurement.flowSheet;

import ca.kai.measurement.flowSheet.xml.FlowSheetItem;
import ca.kai.measurement.flowSheet.xml.MeasurementFlowSheet;
import ca.kai.util.XmlParser;

import org.jdom.Element;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.core.env.Environment;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.stereotype.Service;

import java.io.*;
import java.util.*;

@Service
@Configurable
public class MeasurementTemplateFlowSheetConfig  {

    @Autowired
    Environment environment;

    @Autowired
    FlowSheetRepository flowSheetRepository;

    @Autowired
    FlowSheetUserCreatedRepository flowSheetUserCreatedRepository;

    @Autowired
    private XmlParser xmlParser = new XmlParser();

    private List<File> flowSheetFiles;

    ArrayList<String> dxTriggers = new ArrayList<String>();
    ArrayList<String> programTriggers = new ArrayList<String>();
    Hashtable<String, ArrayList<String>> dxTrigHash = new Hashtable<String, ArrayList<String>>();
    HashMap<String, ArrayList<String>> programTrigHash = new HashMap<String, ArrayList<String>>();
    Hashtable<String, String> flowsheetDisplayNames = new Hashtable<String, String>();
    ArrayList<String> universalFlowSheets = new ArrayList<String>();

    private static MeasurementTemplateFlowSheetConfig measurementTemplateFlowSheetConfig = new MeasurementTemplateFlowSheetConfig();

    Hashtable<String, MeasurementFlowSheet> flowSheets = null;

    HashMap<String,FlowSheet> flowSheetSettings = null;

    public void afterPropertiesSet() throws Exception {
        measurementTemplateFlowSheetConfig = this;
    }

    @Autowired
    public MeasurementTemplateFlowSheetConfig getInstance() {
        if (flowSheets == null) {
            loadFlowSheets();
        }
        measurementTemplateFlowSheetConfig = this;
        return measurementTemplateFlowSheetConfig;
    }

    public ArrayList<String> getFlowSheetsFromDxCodes(List coll) {
        ArrayList<String> alist = new ArrayList<String>();
        for (int i = 0; i < dxTriggers.size(); i++) {
            String dx = dxTriggers.get(i);
            if (coll.contains(dx) && !alist.contains(dx)) {
                ArrayList<String> flowSheets = getFlowSheetForDxCode(dx);
                for (int j = 0; j < flowSheets.size(); j++) {
                    String flowsheet = flowSheets.get(j);
                    if (!alist.contains(flowsheet)) {
                        alist.add(flowsheet);
                    }
                }
            }
        }

        return alist;
    }

    public ArrayList<String> getUniveralFlowSheets() {
        return universalFlowSheets;
    }

    public Hashtable<String, ArrayList<String>> getDxTrigHash() {
        return dxTrigHash;
    }

    public HashMap<String, ArrayList<String>> getProgramTrigHash() {
        return programTrigHash;
    }

    public String getDisplayName(String name) {
        return flowsheetDisplayNames.get(name);
    }

    public Hashtable<String, String> getFlowSheetDisplayNames(){
        return flowsheetDisplayNames;
    }


    public String addFlowSheet(MeasurementFlowSheet m ){
        if( m.getName() == null || m.getName().equals("")){
            m.setName("U"+(flowSheets.size()+1));
        }

        flowSheets.put(m.getName(),m);
        flowsheetDisplayNames.put(m.getName(), m.getDisplayName());
        if (m.getDxTriggers()!=null) {
            addTriggers(m.getDxTriggers().split(","), m.getName());
        }
        return m.getName();
    }

    public void reloadFlowSheets() {
        dxTriggers = new ArrayList<String>();
        programTriggers = new ArrayList<String>();
        dxTrigHash = new Hashtable<String, ArrayList<String>>();
        programTrigHash = new HashMap<String, ArrayList<String>>();
        flowsheetDisplayNames = new Hashtable<String, String>();
        universalFlowSheets = new ArrayList<String>();
        flowSheets = null;
        flowSheetSettings = null;
        loadFlowSheets();
    }

    void loadFlowSheets() {
        flowSheetFiles = populateFileList();
        flowSheets = new Hashtable<String, MeasurementFlowSheet>();
        flowSheetSettings = new HashMap<String,FlowSheet>();

        for (File flowSheet : flowSheetFiles) {
            MeasurementFlowSheet measurementFlowSheet = (MeasurementFlowSheet) xmlParser.fromXML(MeasurementFlowSheet.class, flowSheet.getAbsolutePath(), true);
            flowSheets.put(measurementFlowSheet.getName(), measurementFlowSheet);
            if (measurementFlowSheet.isUniversal()){
                universalFlowSheets.add(measurementFlowSheet.getName());
            }
            else if (measurementFlowSheet.getDxTriggers() != null) {
                String[] dxTrig = measurementFlowSheet.getDxTriggers().split(",");
                addTriggers(dxTrig, measurementFlowSheet.getName());
            }
            else if (measurementFlowSheet.getProgramTriggers() != null) {
                String[] programTrig = measurementFlowSheet.getProgramTriggers().split(",");
                addProgramTriggers(programTrig,measurementFlowSheet.getName());
            }

            flowsheetDisplayNames.put(measurementFlowSheet.getName(), measurementFlowSheet.getDisplayName());
            FlowSheet temp = flowSheetRepository.findTop1ByName(measurementFlowSheet.getName());
            if(temp!=null) {
                flowSheetSettings.put(measurementFlowSheet.getName(), temp);
            }
        }

        List<FlowSheetUserCreated> flowSheetsUserCreated = flowSheetUserCreatedRepository.getAll();
        for (FlowSheetUserCreated flowSheetUserCreated: flowSheetsUserCreated) {

            MeasurementFlowSheet m = new MeasurementFlowSheet();
            m.setName(flowSheetUserCreated.getName());
            m.setDxTriggers(flowSheetUserCreated.getDxCodeTriggers());
            m.setDisplayName(flowSheetUserCreated.getDisplayName());
            m.setWarningColour(flowSheetUserCreated.getWarningColour());
            m.setRecommendationColour(flowSheetUserCreated.getRecommendationColour());
            flowSheets.put(m.getName(), m);
            String[] dxTrig = null;
            if (m.getDxTriggers()!=null){
                dxTrig = m.getDxTriggers().split(",");
            }
            addTriggers(dxTrig, m.getName());
            flowsheetDisplayNames.put(m.getName(), m.getDisplayName());
            FlowSheet tmp = flowSheetRepository.findTop1ByName(m.getName());
            if(tmp!=null) {
                flowSheetSettings.put(m.getName(), tmp);
            }
        }

        for(FlowSheet fs:flowSheetRepository.findAll()) {
            if(fs.isExternal()){
                continue;
            }
            String data = fs.getContent();
            InputStream is = null;
            try {
                is = new ByteArrayInputStream(data.getBytes("UTF-8"));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            MeasurementFlowSheet d = createFlowSheet(is);
            flowSheets.put(d.getName(), d);
            if (d.isUniversal())
                universalFlowSheets.add(d.getName());
            else if(d.getDxTriggers()!=null){
                String[] dxTrig = d.getDxTriggers().split(",");
                addTriggers(dxTrig, d.getName());
            } else if(d.getProgramTriggers()!=null) {
                String[] programTrig = d.getProgramTriggers().split(",");
                addProgramTriggers(programTrig,d.getName());
            }
            flowsheetDisplayNames.put(d.getName(), d.getDisplayName());
            flowSheetSettings.put(d.getName(),fs);
        }

    }

    private List<File> populateFileList() {
        List<File> files = new ArrayList<>();
        ClassLoader cl = this.getClass().getClassLoader();
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver(cl);
        List<Resource> resources = new ArrayList<>();

        try {
            if (environment.getProperty("measurement.flowSheets") != null && environment.getProperty("measurement.flowSheets").contains(",")){
                for (String fileName :  environment.getProperty("measurement.flowSheets").split(",")){
                    resources.add(resolver.getResource("/measurements/flowSheets/"+fileName));
                }
            }


            if (!resources.isEmpty()){
                for (Resource resource: resources){
                    files.add(resource.getFile());
                }
            }
        }
        catch (IOException ioe){
            ioe.printStackTrace();
        }

        return files;
    }

    public HashMap<String,FlowSheet> getFlowSheetSettings() {
        return flowSheetSettings;
    }

    public ArrayList<String> getFlowSheetForDxCode(String code) {
        return dxTrigHash.get(code);
    }

    public ArrayList<String> getFlowSheetForProgramId(String code) {
        return programTrigHash.get(code);
    }

    private void addTriggers(String[] dxTrig, String name) {
        if (dxTrig != null) {
            for (String aDxTrig : dxTrig) {
                if (!dxTriggers.contains(aDxTrig)) {
                    dxTriggers.add(aDxTrig);
                }
                if (dxTrigHash.containsKey(aDxTrig)) {
                    ArrayList<String> l = dxTrigHash.get(aDxTrig);
                    if (!l.contains(name)) {
                        l.add(name);
                    }
                } else {
                    ArrayList<String> l = new ArrayList<String>();
                    l.add(name);
                    dxTrigHash.put(aDxTrig, l);
                }
            }
        }
    }

    private void addProgramTriggers(String[] programTrig, String name) {
        if (programTrig != null) {
            for (String aProgramTrig : programTrig) {
                if (!programTriggers.contains(aProgramTrig)) {
                    programTriggers.add(aProgramTrig);
                }
                if (programTrigHash.containsKey(aProgramTrig)) {
                    ArrayList<String> l = programTrigHash.get(aProgramTrig);
                    if (!l.contains(name)) {
                        l.add(name);
                    }
                } else {
                    ArrayList<String> l = new ArrayList<String>();
                    l.add(name);
                    programTrigHash.put(aProgramTrig, l);
                }
            }
        }
    }


    private MeasurementFlowSheet createFlowSheet(InputStream is) {
        MeasurementFlowSheet measurementFlowSheet = (MeasurementFlowSheet) xmlParser.fromInput(MeasurementFlowSheet.class, is);
        return measurementFlowSheet;
    }


    public MeasurementFlowSheet getFlowSheet(String flowsheetName) {
        return flowSheets.get(flowsheetName);
    }

    public MeasurementFlowSheet getFlowSheet(String flowsheetName, List<FlowSheetCustomization> list) {

        if (list.size() > 0){

            try{
                MeasurementFlowSheet personalizedFlowsheet = makeNewFlowsheet(getFlowSheet(flowsheetName));
                for (FlowSheetCustomization cust : list){
                    if (FlowSheetCustomization.ADD.equals(cust.getAction())){
                        FlowSheetItem item = getItemFromString(cust.getPayload());

                        personalizedFlowsheet.addAfter(cust.getMeasurement(), item);
                    }
                    else if (FlowSheetCustomization.UPDATE.equals(cust.getAction())){
                        FlowSheetItem item = getItemFromString(cust.getPayload());
                        personalizedFlowsheet.updateMeasurementFlowSheetInfo(item);
                    }
                    else if (FlowSheetCustomization.DELETE.equals(cust.getAction())){
                        personalizedFlowsheet.setToHidden(cust.getMeasurement());
                    }
                }

                return personalizedFlowsheet;
            }catch(Exception e){
                e.printStackTrace();
            }
        }
        return getFlowSheet(flowsheetName);
    }

    public List<File> getFlowSheetFiles() {
        return flowSheetFiles;
    }

    public void setFlowSheetFiles(List<File> flowSheetFiles) {
        this.flowSheetFiles = flowSheetFiles;
    }

    public FlowSheetItem getItemFromString(String payload){
        FlowSheetItem item = null;
        InputStream is = null;
        try {
            is = new ByteArrayInputStream(payload.getBytes("UTF-8"));
            item = (FlowSheetItem) xmlParser.fromInput(FlowSheetItem.class, is);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        return item;
    }

    public MeasurementFlowSheet makeNewFlowsheet(MeasurementFlowSheet mFlowsheet) throws Exception{

        ByteArrayOutputStream byteArrayout = new ByteArrayOutputStream();
        xmlParser.toXML(mFlowsheet, byteArrayout);

        InputStream is = new ByteArrayInputStream(byteArrayout.toByteArray());

        return createFlowSheet(is);

    }

    private void addAttributeifValueNotNull(Element element, String attr, String value) {
        if (value != null) {
            element.setAttribute(attr, value);
        }
    }
}
