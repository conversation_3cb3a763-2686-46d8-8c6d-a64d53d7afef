package ca.kai.systemPreference;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/systemPreferences")
public class SystemPreferenceController {

  @Autowired
  private SystemPreferenceService systemPreferenceService;

  @PreAuthorize("hasPermission(#request, '', 'r')")
  @RequestMapping(path = "/readBooleanPreference/{name}", method = RequestMethod.GET, produces = {MediaType.APPLICATION_JSON_VALUE})
  public Boolean readBooleanPreference(final HttpServletRequest request,
                                       final @PathVariable(name = "name") String name,
                                       final @RequestParam(name = "defaultVal", required = false) Boolean defaultVal) {
    return defaultVal != null ? systemPreferenceService.readBooleanPreference(name, defaultVal) : systemPreferenceService.readBooleanPreference(name);
  }

  @PreAuthorize("hasPermission(#request, '', 'r')")
  @RequestMapping(path = "/readStringPreference/{name}", method = RequestMethod.GET, produces = {MediaType.APPLICATION_JSON_VALUE})
  public String readStringPreference(final HttpServletRequest request,
      final @PathVariable(name = "name") String name) {
    SystemPreference preference =  systemPreferenceService.getByName(name);
    if (preference == null) {
      return null;
    }

    return preference.getValue();
  }

  @PreAuthorize("hasPermission(#request, '', 'r')")
  @PostMapping(
      path = "/readMultipleStringPreferences",
      produces = {MediaType.APPLICATION_JSON_VALUE})
  public Map<String, String> readMultipleStringPreferences(final HttpServletRequest request,
      final @RequestBody List<String> names) {

    List<SystemPreference> preferences =  systemPreferenceService.getByNames(names);

    val preferencesMap = new HashMap<String, String>();
    for (SystemPreference preference : preferences) {
      preferencesMap.put(preference.getName(), preference.getValue());
    }
    return preferencesMap;
  }

  @PreAuthorize("hasPermission(#request, '_admin', 'w')")
  @PutMapping(path = "/updatePreference/{name}")
  public void updatePreference(final HttpServletRequest request,
                               final @PathVariable(name = "name") String name,
                               final @RequestBody String value) {
    systemPreferenceService.savePreference(name, value);
  }
}
