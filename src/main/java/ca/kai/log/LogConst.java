package ca.kai.log;

public class LogConst {
    public static final String LOGIN = "log in";
    public static final String LOGOUT = "log out";
    public static final String SHELTER_SELECTION="select shelter";
    public static final String READ = "read";
    public static final String ADD = "add";
    public static final String UPDATE = "update";
    public static final String DELETE = "delete";
    public static final String DISCONTINUE = "discontinue";
    public static final String ARCHIVE = "archive";
    public static final String REPRINT = "reprint";
    public static final String REVIEWED = "reviewed";
    public static final String ACK = "acknowledge";
    public static final String NORIGHT= "no right";
    public static final String EDIT = "edit";
    public static final String REPRESCRIBE = "represcribe";
    public static final String ANNOTATE = "annotate";
    public static final String VERIFY = "verify";
    public static final String REFUSED = "refused";
    public static final String UNLINK = "unlink";
    public static final String SENT = "sent";
    public static final String CLOSED = "closed";
    public static final String SIMULATE = "simulate";
    public static final String GENERATE = "generate";
    public static final String DOWNLOAD = "download";
    public static final String UPLOAD = "upload";
    public static final String OAUTH_LOGIN = "OAuth Login";

    public static final String CON_LOGIN_AGREEMENT = "login agreement";
    public static final String CON_LOGIN = "login";
    public static final String CON_APPT = "appointment";
    public static final String CON_REPEAT_APPT = "repeat appointment";
    public static final String CON_ECHART = "eChart";
    public static final String CON_DEMOGRAPHIC = "demographic";
    public static final String CON_DEMOGRAPHIC_RELATION = "demographic_relations";
    public static final String CON_ROLE = "role";
    public static final String CON_PRIVILEGE = "privilege";
    public static final String CON_FORM = "form";
    public static final String CON_PRESCRIPTION = "prescription";
    public static final String CON_MEDICATION = "medication";
    public static final String CON_DRUGS="drugs";
    public static final String CON_DRUGREASON="drugReason";
    public static final String CON_ALLERGY = "allergy";
    public static final String CON_JASPERREPORTLETER = "jr_letter";
    public static final String CON_TICKLER = "tickler";
    public static final String CON_CME_NOTE = "CME note";
    public static final String CON_DOCUMENT = "document";
    public static final String CON_HL7_LAB = "lab";
    public static final String CON_HRM = "hrm";
    public static final String CON_CML_LAB = "cml lab";
    public static final String CON_MDS_LAB = "mds lab";
    public static final String CON_PATHNET_LAB = "pathnet lab";
    public static final String CON_FLOWSHEET = "FLWST_";
    public static final String CON_SECURITY = "securityRecord";
    public static final String CON_ANNOTATION = "annotation";
    public static final String CON_PHR  = "phr";
    public static final String CON_DOCUMENTDESCRIPTIONTEMPLATE = "documentDescriptionTemplate";
    public static final String CON_DOCUMENTDESCRIPTIONTEMPLATEPREFERENCE = "documentDescriptionTemplatePreference";
    public static final String CON_FAX  = "fax";
    public static final String CON_PHARMACY  = "pharmacy";
    public static final String CON_BILL = "bill";
    public static final String CON_OHIP = "ohip file";
    public static final String CON_MCEDT_MAILBOX = "mcedt mailbox";
    public static final String CON_OAUTH_LOGIN = "Oauth Authentication";
  public static final String SYNC = "sync";
  public static final String PUSH_TO_PORTAL = "writeManualSync";
  public static final String WRITE_AUTO_SYNC_DATE = "writeAutoSyncDate";
}

