package ca.kai.hrm.util;

import ca.kai.OscarPropertyTransition;
import ca.kai.authentication.AuthenticationService;
import ca.kai.hrm.HRMService;
import ca.kai.messages.Message;
import ca.kai.messages.MessageService;
import ca.kai.messages.OscarMessageType;
import ca.kai.provider.Provider;
import ca.kai.util.ssh.SFTPClient;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.FileHandler;
import javax.annotation.Resource;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class HRMFetchService {
    @Autowired
    HRMService hrmService;
    @Autowired
    MessageService messageService;
    @Autowired
    private OscarPropertyTransition propertyTransition;

    @Value("${oscar-classic.OMD_HRM_USER:#{null}}")
    private String OMD_HRM_USER;
    @Value("${oscar-classic.OMD_HRM_IP:#{null}}")
    private String OMD_HRM_IP;
    @Value("${oscar-classic.OMD_HRM_PORT:#{null}}")
    private Integer OMD_HRM_PORT;
    @Value("${oscar-classic.DOCUMENT_DIR:#{null}}")
    private String DOCUMENT_DIR;
    @Value("${oscar-classic.OMD_DIRECTORY:#{null}}")
    private String OMD_DIRECTORY;
    @Value("${oscar-classic.OMD_DOWNLOADS_DIRECTORY:#{null}}")
    private String OMD_DOWNLOADS_DIRECTORY ;
    @Value("${oscar-classic.OMD_HRM_REMOTE_DIR:#{null}}")
    private String OMD_HRM_REMOTE_DIR;
    @Value("${oscar-classic.DECRYPTION_KEY:#{null}}")
    private String DECRYPTION_KEY;
    @Value("${oscar-classic.OMD_HRM_AUTH_KEY_FILE:#{null}}")
    private String OMD_HRM_AUTH_KEY_FILE;
    @Value("${oscar-classic.OMD_LOG_DIRECTORY:#{null}}")
    private String OMD_LOG_DIRECTORY;
    
    private String OMD_XSD;
    private String REPORT_MANAGER_XSD;

    private SimpleDateFormat dayMonthYear = new SimpleDateFormat("ddMMyyyy");
    private Boolean doDecrypt = false;
    /**
     * List of provider numbers who don't want messages regarding outages
     * This is cleared each time the run succeeds as it would be a new outage after one success.
     */
    private List<String> doNotSendOutageMsg = new ArrayList<>();
    private java.util.logging.Logger omdConnectionLog;
    
    public static Boolean isFetching = false;
    private static Logger logger = LoggerFactory.getLogger(HRMFetchService.class);
    private static String todayDownloadsPath = "";
    private static final int DEFAULT_HRM_PORT = 22;
    
    @Resource
    SFTPClient sftpClient;
    
    public HRMFetchService() {
        
    }

    /**
     * Initialize global folder paths
     */
    @Autowired
    private void init() {
    this.OMD_HRM_USER = propertyTransition.getProperty(OMD_HRM_USER, "OMD_HRM_USER");
    this.OMD_HRM_IP = propertyTransition.getProperty(OMD_HRM_IP, "OMD_HRM_IP");
    this.OMD_HRM_PORT =
        propertyTransition.getIntegerProperty(OMD_HRM_PORT, "OMD_HRM_PORT", DEFAULT_HRM_PORT);
    this.DOCUMENT_DIR = propertyTransition.getProperty(DOCUMENT_DIR, "DOCUMENT_DIR");
    this.OMD_DIRECTORY = propertyTransition.getProperty(OMD_DIRECTORY, "OMD_directory");
    this.OMD_DOWNLOADS_DIRECTORY =
        propertyTransition.getProperty(OMD_DOWNLOADS_DIRECTORY, "OMD_downloads");
    this.OMD_HRM_REMOTE_DIR =
        propertyTransition.getProperty(OMD_HRM_REMOTE_DIR, "OMD_HRM_REMOTE_DIR");
    this.DECRYPTION_KEY = propertyTransition.getProperty(DECRYPTION_KEY, "OMD_HRM_DECRYPTION_KEY");
    this.OMD_HRM_AUTH_KEY_FILE =
        propertyTransition.getProperty(OMD_HRM_AUTH_KEY_FILE, "OMD_HRM_AUTH_KEY_FILENAME");
    this.OMD_LOG_DIRECTORY = propertyTransition.getProperty(OMD_LOG_DIRECTORY, "OMD_log_directory");

        this.OMD_XSD = OMD_DIRECTORY + "ontariomd_cds_dt.xsd";
        this.REPORT_MANAGER_XSD = OMD_DIRECTORY + "report_manager_cds.xsd";
    }

    /**
     * Adds the currently logged in user to the do not send list for the current outage
     * @param request
     */
    public void addToDoNotSendList(HttpServletRequest request) {
        Provider loggedInProvider = AuthenticationService.getAuthenticatedProviderFromRequest(request);
        if (loggedInProvider != null && StringUtils.trimToNull(loggedInProvider.getProviderNo()) != null && !doNotSendOutageMsg.contains(loggedInProvider.getProviderNo())) {
            this.doNotSendOutageMsg.add(loggedInProvider.getProviderNo());
        }
    }

    /**
     * Connects to the OMD HRM server directory via SFTP, downloads files, 
     * decrypts them if there is a decryption key specified,
     * and adds the HRMs to the appropriate inbox.
     * @param request
     * @throws Exception
     */
    public synchronized void fetch(HttpServletRequest request) throws Exception {
        this.doDecrypt = StringUtils.isNotEmpty(DECRYPTION_KEY);
        Provider loggedInProvider = AuthenticationService.getAuthenticatedProviderFromRequest(request);
        
        try {
            createDirectoryAndLogger();
        } catch (Exception e) {
            logger.error("",e);
        }

        if (!isFetching) {
            try {
                // Instantiate a new SFTP connection
                sftpClient = new SFTPClient(OMD_HRM_IP, OMD_HRM_PORT, OMD_HRM_USER);
                sftpClient.setPrivateKey(OMD_DIRECTORY + OMD_HRM_AUTH_KEY_FILE);

                sftpClient.connect();
                sftpClient.openChannel();
                omdConnectionLog.info("SFTP connection established with " + OMD_HRM_IP + ":" + OMD_HRM_PORT + ".");

                isFetching = true;

                // Fetch data from OMD
                logger.info("HRMFetchService, remoteDir:" + OMD_HRM_REMOTE_DIR);

                List<String> localFilePaths = new ArrayList<>();
                String[] files = sftpClient.listFiles(OMD_HRM_REMOTE_DIR);
                
                for (String file : files) {
                    if (file != null) {
                        sftpClient.download(OMD_HRM_REMOTE_DIR + file, todayDownloadsPath + file);
                        localFilePaths.add(todayDownloadsPath + file);
                        omdConnectionLog.info("Downloaded File: " + todayDownloadsPath + file);
                    }
                }
                
                String[] localFiles = localFilePaths.toArray(new String[0]);
                if(doDecrypt) {
                    localFiles = decryptFiles(localFiles);
                }

                // Delete downloaded files from remote directory
                sftpClient.removeDirectoryContents(OMD_HRM_REMOTE_DIR, files);
                
                // Disconnect
                sftpClient.disconnect();

                List<String> hrmDocumentPaths = new ArrayList<>();
                for (String file : localFiles) {
                    try {
                        FileUtils.copyFileToDirectory(new File(file), new File(DOCUMENT_DIR));
                        hrmDocumentPaths.add(file);
                    } catch(IOException e) {
                        logger.error("Error copying HRM file. Will not be viewable from Inbox!",e);
                        notifyHrmError(loggedInProvider, "Failed to copy HRM file to DOCUMENT_DIR. Please contact admin. ("+ file +")");
                    }
                }

                // parse each report and add it to the inbox
                hrmDocumentPaths.forEach(file -> hrmService.addReportToInbox(HRMParser.parseReport(new File(DOCUMENT_DIR, new File(file).getName()).getAbsolutePath())));
                
                
                // disconnect from sftp
                sftpClient.disconnect();
                // clear do not send list
                doNotSendOutageMsg.clear();
            } catch (Exception e) {
                isFetching = false;
                if (sftpClient != null) {
                    sftpClient.disconnect();
                }
                logger.error("Couldn't perform SFTP fetch for HRM - notifying user of failure", e);
                notifyHrmError(loggedInProvider, e.getMessage());
                throw e;
            }
            
            isFetching = false;
        } else {
            logger.warn("There is currently an HRM fetch running -- will not run another until it has completed or timed out.");
        }
    }

    /**
     * Creates HRM failure notification messages
     * @param provider
     * @param errorMsg
     */
    protected void notifyHrmError(Provider provider, String errorMsg) {
        ArrayList<String> sendToProviders = new ArrayList<String>();
        String adminProvider = "999998";
        
        if (provider != null) {
            // manual prompts always send to admin
            sendToProviders.add(adminProvider);
            
            if (!doNotSendOutageMsg.contains(provider.getProviderNo()) && !adminProvider.equals(provider.getProviderNo())) {
                sendToProviders.add(provider.getProviderNo());
            }
        } else if (!doNotSendOutageMsg.contains(adminProvider)) {
            sendToProviders.add(adminProvider);
        }

        
        if (!sendToProviders.isEmpty()) {
            // if there are message recipients, create and send message
            String messageStr = "OSCAR attempted to perform a fetch of HRM data at " + new Date() 
                    + " but there was an error during the task.\n\nSee below for the error message:\n" + errorMsg;
            Message outageMessage = new Message(messageStr, "HRM Retrieval Error", "System", -1, null, OscarMessageType.GENERAL_TYPE);
            
            messageService.send(outageMessage, sendToProviders);
        }
    }

    /**
     * Creates the daily local temporary directory for HRM downloads
     * and the logger
     * @throws Exception
     */
    private void createDirectoryAndLogger() throws Exception {
        Date today = new Date();
        // create new logger
        try {
            FileHandler handler = new FileHandler(OMD_LOG_DIRECTORY + dayMonthYear.format(today) + ".log", true);
            omdConnectionLog = java.util.logging.Logger.getLogger("HRMFetchService");
            omdConnectionLog.addHandler(handler);
        } catch (IOException ioe) {
            logger.error("Failed to create OMD log", ioe);
        }

        // Create directory if it does not exist
        File dir = new File(OMD_DOWNLOADS_DIRECTORY + dayMonthYear.format(today));
        todayDownloadsPath = dir.getAbsolutePath() + "/";
        if (!dir.exists() && !dir.mkdir()) {
            throw new Exception("Unable to create directory " + dir.getAbsolutePath() + ". Please check permissions.");
        }
    }

    /**
     * Decrypts file using the decryption key specified
     * @param fullPath
     * @return
     * @throws Exception
     */
    private String decryptFile(String fullPath) throws Exception {
        logger.debug("About to decrypt: " + fullPath);
        File encryptedFile = new File(fullPath);
        if (!encryptedFile.exists()) {
            throw new Exception("Could not find file '" + fullPath + "' to decrypt.");
        }

        //get the bytes of the file in an array
        byte[] fileInBytes = new byte[(int) encryptedFile.length()];
        
        try (FileInputStream fis = new FileInputStream(encryptedFile)) {
            fis.read(fileInBytes);
        } 

        //the provided key is 32 characters long string hex representation of a 128 hex key, get the 128-bit hex bytes
        SecretKeySpec key = new SecretKeySpec(toHex(DECRYPTION_KEY.toCharArray()), "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding", "SunJCE");
        cipher.init(Cipher.DECRYPT_MODE, key);
        
        return new String(cipher.doFinal(fileInBytes));
    }

    /**
     * Reads and decrypts files
     * @param files
     * @return
     * @throws Exception
     */
    private String[] decryptFiles(String[] files) throws Exception {
        if (files.length == 0) {
            return new String[0];
        }

        String[] decryptedFilePaths = new String[files.length];
        
        
        // Create new directory for decrypted files if it does not already exist
        String todayDecryptedDownloadsDir = todayDownloadsPath + "/" +  "decrypted";
        File dir = new File(todayDecryptedDownloadsDir);
        if (!dir.exists() && !dir.mkdir()) {
            throw new Exception("Unable to create directory " + dir.getAbsolutePath() + ". Please check permissions.");
        }


        for (int i = 0; i < files.length; i++){
            String file = files[i];
            if (file != null) {
                String decryptedContent = null;
                String filename = "";
                String filePath = "";
                
                FileWriter fileWriter = null;
                BufferedWriter bufferedWriter = null;
                
                try {
                    decryptedContent = decryptFile(file);
                    filename = file.substring(file.lastIndexOf("/"));
                    filePath = todayDecryptedDownloadsDir + filename;
                    
                    fileWriter = new FileWriter(filePath);
                    bufferedWriter = new BufferedWriter(fileWriter);
                    bufferedWriter.write(decryptedContent);
                    decryptedFilePaths[i] = filePath;
                } catch(Exception e) {
                    logger.error("Error decrypting file - " + file);
                } finally {
                    if (bufferedWriter != null) {
                        bufferedWriter.close();
                    }
                    if (fileWriter != null) {
                        fileWriter.close();
                    }
                }
            }
        }
        

        return decryptedFilePaths;
    }

    /**
     * Converts the decryption key to hexadecimal byte array
     * @param enc
     * @return
     */
    private static byte[] toHex(char[] enc) {
        if ((enc.length % 2) != 0){
            throw new IllegalArgumentException("Decryption key must contain an even number of characters");
        }
        
        final byte[] data = new byte[enc.length / 2];
        for (int i = 0; i < enc.length; i += 2) {
            data[i / 2] = (byte) ((Character.digit(enc[i], 16) << 4) + Character.digit(enc[i + 1], 16));
        }
        
        return data;
    }

}
