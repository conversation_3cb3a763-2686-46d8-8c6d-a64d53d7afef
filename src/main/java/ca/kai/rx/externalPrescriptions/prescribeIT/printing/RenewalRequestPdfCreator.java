package ca.kai.rx.externalPrescriptions.prescribeIT.printing;

import ca.kai.demographic.Demographic;
import ca.kai.integration.eRx.ClinicConfig;
import ca.kai.integration.eRx.ProviderConfig;
import ca.kai.pharmacy.PharmacyInfo;
import ca.kai.provider.Provider;
import ca.kai.rx.drug.Drug;
import ca.kai.rx.externalPrescriptions.prescribeIT.ErxMessageService;
import ca.kai.rx.externalPrescriptions.prescribeIT.RxFillRequestService;
import ca.kai.rx.externalPrescriptions.prescribeIT.Task;
import ca.kai.rx.externalPrescriptions.prescribeIT.TaskRepository;
import ca.kai.rx.prescription.TaskReason;
import lombok.val;
import net.sf.jasperreports.engine.JREmptyDataSource;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperExportManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.apache.commons.lang3.StringUtils;
import org.hl7.fhir.exceptions.FHIRException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URISyntaxException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class RenewalRequestPdfCreator {
    final String RESOURCE_PATH = "rx/externalPrescriptions/prescribeIT/";

    private TaskRepository taskRepository;
    private ErxMessageService erxMessageService;
    private final Logger logger = LoggerFactory.getLogger(RenewalRequestPdfCreator.class);

    @Autowired
    public RenewalRequestPdfCreator(TaskRepository taskRepository, ErxMessageService erxMessageService) {
        this.taskRepository = taskRepository;
        this.erxMessageService = erxMessageService;
    }

    /**
     * Creates a renewal response pdf based off of the provided List of tasks and logged in provider
     * @param renewalRequests A list of renewal responses to print
     * @param loggedInProvider The creating logged in provider
     * @param siteId The siteId of the clinic when multisites is enabled, null when disabled
     * @return a byte array containing the pdf data
     */
    public byte[] createPdf(
        final List<Task> renewalRequests,
        final Provider loggedInProvider,
        final Integer siteId
    ) {
        val pdfParameters = creatReportParameterMap(renewalRequests, loggedInProvider, siteId);
        byte[] pdfBytes = new byte[0];

        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            // Load the jasper report object
            String reportUri = RESOURCE_PATH + "kaiemrErxRenewalRequest.jrxml";
            JasperReport report = JasperCompileManager.compileReport(getClass().getClassLoader().getResource(reportUri).toURI().getPath());
            
            // Fill report parameters
            JasperPrint jasperPrint = JasperFillManager.fillReport(report, pdfParameters, new JREmptyDataSource());
            
            // Write pdf to output stream
            bos.write(JasperExportManager.exportReportToPdf(jasperPrint));
            pdfBytes = bos.toByteArray();
        } catch (IOException | JRException | URISyntaxException e) {
            e.printStackTrace();
        }

        return pdfBytes;
    }

    /**
     * Creates a Map of parmeters to be provided to the jasperreport
     * @param renewalRequests A list of renewal responses to print
     * @param loggedInProvider The creating logged in provider
     * @param siteId The siteId of the clinic when multisites is enabled, null when disabled
     * @return a map of printing parameters
     */
    private Map<String, Object> creatReportParameterMap(
        final List<Task> renewalRequests,
        final Provider loggedInProvider,
        final Integer siteId
    ) {
        SimpleDateFormat sdf = new SimpleDateFormat("MMMM d, yyyy");
        Map<String, Object> parameterMap = new HashMap<>();
        
        Demographic subjectDemographic = renewalRequests.get(0).getDemographic();
        val clinicConfig = ClinicConfig.getInstance();
        val clinic = clinicConfig.getClinic(siteId);
        PharmacyInfo pharmacyInfo = null;

        parameterMap.put("clinicName", clinic.getName());
        parameterMap.put("clinicAddress", clinic.getAddress());
        parameterMap.put("clinicCity", clinic.getCity());
        parameterMap.put("clinicProvince", clinic.getProvince());
        parameterMap.put("clinicPostal", clinic.getPostalCode());
        parameterMap.put("providerPractitionerNo", loggedInProvider.getPractitionerNo());
        parameterMap.put("clinicPhone", clinic.getPhone());
        parameterMap.put("clinicFax", clinic.getFax());
        parameterMap.put("printDate", sdf.format(new Date()));
        parameterMap.put("demographicName", subjectDemographic.getFormattedName());
        parameterMap.put("demographicAddress", subjectDemographic.getAddress());
        parameterMap.put("demographicDob", subjectDemographic.getDOB());
        parameterMap.put("demographicCity", subjectDemographic.getCity());
        parameterMap.put("demographicPostal", subjectDemographic.getPostal());
        parameterMap.put("demographicPhone", subjectDemographic.getPhone());
        parameterMap.put("demographicHin", subjectDemographic.getHin());

        List<RenewalRequestRow> renewalRequestList = new ArrayList<>();
        ProviderConfig providerConfig =
            ProviderConfig.getInstance(loggedInProvider.getProviderNo());
        for (Task renewalRequest : renewalRequests) {
            RenewalRequestRow row = new RenewalRequestRow();
            if (renewalRequest.getBasedOn() == null) {
                logger.error(
                    "Could not retrieve base request information for request "
                        + renewalRequest.getId());
            }
            String basedOnUuid = renewalRequest.getBasedOn().replaceAll("urn:uuid:", "");
            Task basedOnTask = taskRepository.getByUuid(basedOnUuid);
            Drug requestedDrug = null;

            if (!parameterMap.containsKey("providerName")) {
                StringBuilder printName = new StringBuilder(loggedInProvider.getFirstName())
                    .append(" ")
                    .append(loggedInProvider.getLastName());
                if (!providerConfig.isEnabled() && basedOnTask.getProvider() != null) {
                    printName.append(" on behalf of ")
                        .append(basedOnTask.getProvider().getFirstName())
                        .append(" ")
                        .append(basedOnTask.getProvider().getLastName());
                }
                parameterMap.put("providerName", printName.toString());
            }
            
            if (renewalRequest.getDrug() != null) {
                requestedDrug = renewalRequest.getDrug();
            } else {
                try {
                    // Tries to get the drug from the renewal request so set as the requestedDrug
                    requestedDrug = erxMessageService.getPrescriptionMedicationInfo(basedOnTask.getMessageHeader().getXmlFileName(), basedOnTask.getUuid());
                    Drug drugFromDispense = erxMessageService.createDrugFromDispenses(requestedDrug);
                    if(drugFromDispense != null) {
                        requestedDrug = drugFromDispense;
                    }
                } catch (FHIRException | IOException e) {
                    e.printStackTrace();
                }
            }

            try {
                //Get the source pharmacy info from request
                if (pharmacyInfo == null) {
                    pharmacyInfo = erxMessageService.getSourcePharmacyInfo(basedOnTask.getMessageHeader().getXmlFileName(), basedOnTask.getMessageHeader().getUuid());
                }
            } catch(IOException e) {
                logger.error("Could not retrieve source pharmacy information for request " + renewalRequest.getId());
            }
            
            // If the requested drug exists, uses it's special for the display, else sets the special to blank
            if (requestedDrug != null) {
                if (requestedDrug.getSpecial() != null && requestedDrug.getName() != null && !requestedDrug.getSpecial().startsWith(requestedDrug.getName())) {
                    row.setRequestedRxSpecial (requestedDrug.getName() + "\n" + requestedDrug.getSpecial());
                } else {
                    row.setRequestedRxSpecial(requestedDrug.getFormattedDrugInfo());
                }
            } else {
                row.setRequestedRxSpecial("");
            }
            
            row.setRequestedRxAction(RxFillRequestService.RXTaskRequest.getByCode(renewalRequest.getType()).getDescription());
            if (renewalRequest.getReason() != null) {
                row.setRequestedRxActionReason(StringUtils.trimToEmpty(TaskReason.getByConceptCode(renewalRequest.getReason()).getDisplayName()));
            }
            row.setRequestedRxActionReasonText(StringUtils.trimToEmpty(renewalRequest.getReasonText()));
            renewalRequestList.add(row);
        }
        parameterMap.put("RenewalRequestList", new JRBeanCollectionDataSource(renewalRequestList));
        if(pharmacyInfo != null) {
            parameterMap.put("pharmacyName", pharmacyInfo.getName());
            parameterMap.put("pharmacyCity", pharmacyInfo.getCity());
            parameterMap.put("pharmacyProvince", pharmacyInfo.getProvince());
            parameterMap.put("pharmacyPostal", pharmacyInfo.getPostalCode());
            parameterMap.put("organizationId", pharmacyInfo.getOrganizationId());
            parameterMap.put("pharmacyPhone", pharmacyInfo.getPhone1());
            parameterMap.put("pharmacyFax", pharmacyInfo.getFax());// 
            parameterMap.put("pharmacyAddress", pharmacyInfo.getAddress());
        }
        
        return parameterMap;
    }
}
