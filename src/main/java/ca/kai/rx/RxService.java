package ca.kai.rx;

import static ca.kai.rx.externalPrescriptions.prescribeIT.RxFillRequestService.RXTaskRequest.CANCEL;

import ca.kai.OscarProperties;
import ca.kai.attachmentmanager.printer.SimplePrinter;
import ca.kai.attachmentmanager.printer.SimplePrinterUtils;
import ca.kai.clinic.Clinic;
import ca.kai.clinic.ClinicService;
import ca.kai.datasharing.DataSharingService;
import ca.kai.datasharing.ResourceTypeEnum;
import ca.kai.demographic.Allergy;
import ca.kai.demographic.AllergyRepository;
import ca.kai.demographic.Demographic;
import ca.kai.demographic.DemographicPharmacy;
import ca.kai.demographic.DemographicPharmacyRepository;
import ca.kai.demographic.DemographicRepository;
import ca.kai.fax.enums.FaxFileType;
import ca.kai.fax.enums.FaxStatusInternal;
import ca.kai.fax.exceptions.FaxException;
import ca.kai.fax.service.FaxMigrationService;
import ca.kai.integration.eRx.ClinicConfig;
import ca.kai.log.LogService;
import ca.kai.pharmacy.PharmacyInfo;
import ca.kai.pharmacy.PharmacyInfoRepository;
import ca.kai.pharmacy.PharmacyServiceType;
import ca.kai.pharmacy.PharmacyServiceTypeRepository;
import ca.kai.property.Property;
import ca.kai.property.PropertyService;
import ca.kai.provider.Provider;
import ca.kai.provider.ProviderService;
import ca.kai.provider.providerSignature.ProviderSignatureRepository;
import ca.kai.rx.drug.CancelStatusDisplay;
import ca.kai.rx.drug.Drug;
import ca.kai.rx.drug.DrugArchiveReason;
import ca.kai.rx.drug.DrugDispense;
import ca.kai.rx.drug.DrugRepository;
import ca.kai.rx.drug.controlledSubstance.DemographicIdentificationTypesService;
import ca.kai.rx.drug.instructions.InstructionParser;
import ca.kai.rx.drug.instructions.RxInstructionParser;
import ca.kai.rx.drugDatabases.DrugDatabaseService;
import ca.kai.rx.drugDatabases.DrugInteractionResult;
import ca.kai.rx.externalPrescriptions.prescribeIT.ErxInteractionResponse;
import ca.kai.rx.externalPrescriptions.prescribeIT.ErxInteractionResponseRepository;
import ca.kai.rx.externalPrescriptions.prescribeIT.ErxMessageHeader;
import ca.kai.rx.externalPrescriptions.prescribeIT.ErxMessageHeaderRepository;
import ca.kai.rx.externalPrescriptions.prescribeIT.ErxMessageService;
import ca.kai.rx.externalPrescriptions.prescribeIT.ErxNotificationDisplay;
import ca.kai.rx.externalPrescriptions.prescribeIT.RxFillRequestService;
import ca.kai.rx.externalPrescriptions.prescribeIT.RxFillRequestService.RXTaskRequest;
import ca.kai.rx.externalPrescriptions.prescribeIT.Task;
import ca.kai.rx.externalPrescriptions.prescribeIT.TaskRepository;
import ca.kai.rx.externalPrescriptions.prescribeIT.TaskService;
import ca.kai.rx.prescription.Prescription;
import ca.kai.rx.prescription.PrescriptionPdfCreator;
import ca.kai.rx.prescription.PrescriptionRepository;
import ca.kai.rx.prescription.TaskReason;
import ca.kai.site.Site;
import ca.kai.site.SiteRepository;
import ca.kai.systemPreference.SystemPreferenceService;
import ca.kai.util.PdfUtils;
import ca.kai.util.Printable;
import ca.oscarpro.common.http.OscarProHttpClient;
import ca.oscarpro.common.http.OscarProHttpService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fdb.mkfi.core.IDNotFoundException;
import com.fdb.mkfi.core.NoUnivIDFactoryException;
import com.fdb.mkfi.core.UnivIDLoadException;
import com.lowagie.text.PageSize;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.util.EntityUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class RxService {

  public static final String PARSE_DETAILED_INSTRUCTIONS_URL =
      "%s/ws/services/rxlookup/parseDetailed?instructions=%s";
  private final RxParserLogger rxParserLogger = new RxParserLogger();
  private static final String METHOD_KEY = "Method";
  private static final String TAKEMIN_KEY = "TakeMin";
  private static final String TAKEMAX_KEY = "TakeMax";
  private static final String FREQCODE_KEY = "FreqCode";
  private static final String DURATION_KEY = "Duration";
  private static final String DURATION_UNIT_KEY = "DurationUnit";
  private static final String QUANTITY_KEY = "Quantity";

    @Autowired
    AllergyRepository allergyRepository;
    @Autowired
    DrugDatabaseService drugDatabaseService;
    @Autowired
    DrugRepository drugRepository;
    @Autowired
    PrescriptionRepository prescriptionRepository;
    @Autowired
    PropertyService propertyService;
    @Autowired
    DemographicRepository demographicRepository;
    @Autowired
    ProviderService providerService;
    @Autowired
    ProviderSignatureRepository providerSignatureRepository;
    @Autowired
    DemographicPharmacyRepository demographicPharmacyRepository;
    @Autowired
    PrescriptionPdfCreator prescriptionPdfCreator;
    @Autowired
    FaxMigrationService faxService;
    @Autowired
    ClinicService clinicService;
    @Autowired
    PharmacyInfoRepository pharmacyInfoRepository;
    @Autowired
    PharmacyServiceTypeRepository pharmacyServiceTypeRepository;
    @Autowired
    LogService logService;
    @Autowired
    RxFillRequestService rxFillRequestService;
    @Autowired
    TaskRepository taskRepository;
    @Autowired
    TaskService taskService;
    @Autowired
    ErxMessageService erxMessageService;
    @Autowired
    ErxMessageHeaderRepository erxMessageHeaderRepository;
    @Autowired
    DataSharingService dataSharingService;
    @Autowired
    SiteRepository siteRepository;
    @Autowired
    OscarProperties oscarProperties;
    @Autowired
    SystemPreferenceService systemPreferenceService;
    @Autowired
    ErxInteractionResponseRepository erxInteractionResponseRepository;
    @Autowired
    private SimplePrinter simplePrinter;

  @Autowired
  private OscarProHttpClient oscarProHttpClient;
  @Autowired
  private OscarProHttpService oscarProHttpService;

    public String ePrescribe(
        Prescription prescription,
        final RXTaskRequest rxTaskRequest,
        final String token,
        final String destination,
        final Integer siteId
    ) {
        String entityString = "";
        if (!prescription.getItems().isEmpty()) {
            prescription.setDestinationPharmacy(pharmacyInfoRepository.getByOrganizationId(destination));
            prescription.setDeliveryMethod("ERX");
            if (!prescription.isErxFailedToSend()) { // Failed to send prescriptions do not need to be prepped
                prescription = preparePrescriptionForSave(prescription);
            }
            entityString = rxFillRequestService.prescriptionRequest(prescription, rxTaskRequest, token, destination, siteId).toString();
            StringBuilder drugList = new StringBuilder();

            //If the prescribed drug is a renewal, we want to archive the drug it was represcribed from
            for (Drug drug : prescription.getItems()) {
                if (drug.getPriorRxRefId() != null) {
                    Drug archiveDrug = drugRepository.findOne(Integer.parseInt(drug.getPriorRxRefId()));
                    archiveDrug.archiveDrug(DrugArchiveReason.REPRESCRIBED);
                    drugRepository.save(archiveDrug);
                }
                drugList.append(drug.getSpecial()).append("\n");

                if (StringUtils.isNotEmpty(drug.getPriorRxRefId())) {
                    logService.writeEntry("represcribe",
                        "medication",
                        drug.getId().toString(),
                        drug.getDemographicNo(),
                        drug.getSpecial(),
                        prescription.getProvider());
                }
            }
            logService.writeEntry("add",
                "prescription",
                prescription.getScriptNo().toString(),
                prescription.getDemographicNo(),
                drugList.toString(),
                prescription.getProvider());
        }
        return entityString;
    }

    public Boolean cancelEPrescribe(
        final Integer drugId, final String token, final TaskReason cancelReason,
        final String additionalReasonInfo, final Provider cancellingProvider, final Integer siteId
    ) {
        Drug drug = drugRepository.findOne(drugId);

        if (drug != null) {
            
            // Create copy of drugs prescription with only the drug to be cancelled
            Prescription prescription = prescriptionRepository.findOne(drug.getScriptNo());
            if (prescription != null) {
                List<Task> taskList = new ArrayList<Task>();
                Task emrTask = taskService.createTaskForSend(String.valueOf(prescription.getScriptNo()), CANCEL, cancelReason, additionalReasonInfo, drug,
                        cancellingProvider, prescription.getDestinationPharmacy().getOrganizationId(), null);
                taskList.add(emrTask);

                val taskBundleXml = taskService.createAndSaveTaskBundle(taskList, cancellingProvider, token, prescription.getDestinationPharmacy().getOrganizationId(), siteId);
                if (!erxMessageService.sendTasks(taskBundleXml)) {
                    // Save task
                    erxMessageService.saveMessageHeader(emrTask.getMessageHeader());
                    taskRepository.save(emrTask);
                    return true;
                }
            }
        }
        return false;
    }

    public Drug getFromDrugDatabase(String drugId, Provider loggedInProvider) throws Exception {
        Drug result = drugDatabaseService.getDrug(drugId);
        String defaultQuantity = propertyService.readProperty("rx_default_quantity", loggedInProvider.getProviderNo());
        if (!defaultQuantity.isEmpty()) {
            result.setQuantity(defaultQuantity);
        } else {
            result.setQuantity("0");
        }
        return result;
    }

    /**
     * getDemographicAllergies
     * Retrieve a list of allergy objects for a specific demographic
     * @param demographicNo
     * @return list of allergies
     */
    public List<Allergy> getDemographicAllergies(Integer demographicNo) {
        return allergyRepository.findAllByDemographicNo(demographicNo);
    }

  /**
   * Gets all active demographic allergies from database
   *
   * @param demographicNumber demographic identifier
   * @return list of allergies
   */
  public List<Allergy> getActiveDemographicAllergies(final Integer demographicNumber) {
    return allergyRepository.findAllActiveByDemographicNo(demographicNumber);
  }

    /**
     * Creates a list of drug dispenses
     * @param drugId the drugId of the drug to retrieve dispenses for
     * @return a list of DrugDispense objects
     */
    public List<DrugDispense> getDrugDispenses(Integer drugId) throws Exception {
        List<DrugDispense> drugDispenses = new ArrayList<>();
        List<Task> dispenseNotifications = taskRepository.findTasksByDrugIdAndTypeInOrderByCreateDateDesc(drugId,
                Collections.singletonList(TaskService.RXTaskRequest.DISPENSE_NOTIFICATION.getCode()));
        
        for (Task notificationResponse : dispenseNotifications) {
            Task matchingCancelNotification = taskRepository.findFirstByCancelDispenseTaskByDispenseTaskId(notificationResponse.getId());
            ErxMessageHeader messageHeader = matchingCancelNotification != null ? matchingCancelNotification.getMessageHeader() : notificationResponse.getMessageHeader();

            if (messageHeader != null) {
                String taskUuid = matchingCancelNotification != null ? matchingCancelNotification.getUuid() : notificationResponse.getUuid();
                List<DrugDispense> medicationDispensesFromXml = erxMessageService.getMedicationDispenses(messageHeader.getXmlFileName(), taskUuid);
                
                if (medicationDispensesFromXml != null && !medicationDispensesFromXml.isEmpty()) {
                    if (matchingCancelNotification != null) {
                        medicationDispensesFromXml.forEach(drugDispense -> {
                            drugDispense.setStatus("cancelled");
                            drugDispense.setCancelledDate(matchingCancelNotification.getCreateDate());
                            drugDispense.setCancelledReason(matchingCancelNotification.getReasonText());
                        });
                    }
                    drugDispenses.addAll(medicationDispensesFromXml);
                }
            }
        }
        
        return drugDispenses;
    }

    /**
     * Creates a list of drug interaction warnings based on the provided demographic's drugs.
     * @param demographicNo the demographicNo of the demographic to test
     * @return a list of DrugInteractionResult
     */
    public List<DrugInteractionResult> getDrugInteractionWarnings(Integer demographicNo) {
        List<Drug> demographicDrugs = drugRepository.findAllCurrentByDemographicNo(demographicNo);
        return drugDatabaseService.getDrugInteractionWarnings(demographicDrugs);
    }

  /**
   * Creates a list of drug interaction warnings based on the provided demographic's drugs and the
   * 'subject' that is provided. All results will be interactions specific to the subject drug
   *
   * @param demographicNumber the demographicNo of the demographic to test
   * @param newDrugs          the drugs to check for new warnings against
   * @return a list of DrugInteractionResult
   */
  public List<DrugInteractionResult> getDrugInteractionWarnings(
      final Integer demographicNumber, final List<Drug> newDrugs) {
    return getDrugInteractionWarnings(demographicNumber, newDrugs, new ArrayList<>());
  }

  /**
   * Creates a list of drug interaction warnings based on the provided demographic's drugs and the
   * 'subject' that is provided. All results will be interactions specific to the subject drug
   *
   * @param demographicNumber the demographicNo of the demographic to test
   * @param newDrugs          the drugs to check for new warnings against
   * @param pastDrugs         the patient's past drugs to check for new warnings against
   * @return a list of DrugInteractionResult
   */
  public List<DrugInteractionResult> getDrugInteractionWarnings(
      final Integer demographicNumber,
      final List<Drug> newDrugs,
      List<Drug> pastDrugs
  ) {
    var demographicDrugs = (List<Drug>) new ArrayList<Drug>();

    val rxDateSystemPreference =
        systemPreferenceService.getByName("use_rx_date_for_interaction");
    val useRxDatePreference = rxDateSystemPreference != null
        ? rxDateSystemPreference.getValue()
        : null;

    val dayStringSystemPreference =
        systemPreferenceService.getByName("rx_results_for_number_of_days_in_past");
    val daysString = dayStringSystemPreference != null
        ? dayStringSystemPreference.getValue()
        : null;

    if (oscarProperties.isEnabled("indivicare_link_enabled") && !pastDrugs.isEmpty()) {
      pastDrugs = filterDrugsByCurrent(pastDrugs);
      if (isUsingRxDatePreference(useRxDatePreference, daysString)) {
        val date = getRxDate(daysString);
        pastDrugs = filterDrugsByRxDate(pastDrugs, date);
      }
      return drugDatabaseService.getDrugInteractionWarningsForSubjectDrugs(pastDrugs, newDrugs);
    }

    if (isUsingRxDatePreference(useRxDatePreference, daysString)) {
      val date = getRxDate(daysString);
      demographicDrugs = drugRepository.findAllCurrentByDemographicNoByRxDate(demographicNumber, date);
    } else {
      demographicDrugs = drugRepository.findAllCurrentByDemographicNo(demographicNumber);
    }

    return drugDatabaseService.getDrugInteractionWarningsForSubjectDrugs(demographicDrugs, newDrugs);
  }

  private List<Drug> filterDrugsByRxDate(final List<Drug> drugs, final Date date) {
    return drugs.stream()
        .filter(drug -> isDrugBetweenNowAndDate(drug, date))
        .collect(Collectors.toList());
  }

  private boolean isDrugBetweenNowAndDate(final Drug drug, final Date date) {
    return drug.getRxDate().after(date)
        && drug.getRxDate().before(new Date());
  }

  private List<Drug> filterDrugsByCurrent(final List<Drug> drugs) {
    return drugs.stream()
        .filter(drug -> !drug.getArchived())
        .collect(Collectors.toList());
  }

  private boolean isUsingRxDatePreference(final String useRxDatePreference, final String daysString) {
    return "true".equals(useRxDatePreference) && StringUtils.isNotEmpty(daysString);
  }

  private Date getRxDate(final String daysString) {
    try {
      val days = Integer.parseInt(daysString);
      val cal = Calendar.getInstance();
      cal.add(Calendar.DATE, -days);
      return cal.getTime();
    } catch (NumberFormatException e) {
      log.error("The value for rx_results_for_number_of_days_in_past in the SystemPreferences table "
          + "is not a valid Integer. The current date will be used instead.", e);
      return new Date();
    }
  }

  /**
   * Gets allergy warnings for demographic, either with their current drugs or a future drug
   *
   * @param demographicNumber demographic identifier
   * @param newDrug           future drug
   * @return list of allergies triggered by input drugs
   */
  public List<Allergy> getPatientAllergyWarnings(
      final Integer demographicNumber,
      final Drug newDrug
  ) throws UnivIDLoadException, IDNotFoundException, NoUnivIDFactoryException {
    return getPatientAllergyWarnings(demographicNumber, newDrug, null);
  }

  /**
   * Gets allergy warnings for demographic, either with their current drugs or a future drug
   *
   * @param demographicNumber demographic identifier
   * @param newDrug           future drug
   * @param allergies         allergies if they are passed in from front end intended for linked
   *                          allergies
   * @return list of allergies triggered by input drugs
   */
  public List<Allergy> getPatientAllergyWarnings(
      final Integer demographicNumber,
      final Drug newDrug,
      final List<Allergy> allergies
  ) throws UnivIDLoadException, IDNotFoundException, NoUnivIDFactoryException {
    val demographicAllergies = allergies != null
        ? allergies
        : allergyRepository.findAllActiveByDemographicNo(demographicNumber);
    if (newDrug != null) {
      val prospectiveDrugs = new ArrayList<Drug>();
      prospectiveDrugs.add(newDrug);
      return drugDatabaseService.getAllergyReactionWarnings(demographicAllergies, prospectiveDrugs);
    } else {
      val demographicDrugs = drugRepository.findAllCurrentByDemographicNo(demographicNumber);
      return drugDatabaseService.getAllergyReactionWarnings(demographicAllergies, demographicDrugs);
    }
  }
    
    /**
     * Creates or updates allergy object
     * @param allergy
     * @return the saved allergy
     */
    public Allergy saveAllergy(Allergy allergy, HttpServletRequest request) {
      if (allergy.getEntryDate() == null) {
        allergy.setEntryDate(new Date());
      }
      Date autoSyncDate = dataSharingService.calculateAutoSyncDate(allergy.getEntryDate(),
          ResourceTypeEnum.ALLERGY, allergy.getDemographicNo());
      allergy.setAutoSyncDate(autoSyncDate);
      Allergy savedAllergy = allergyRepository.save(allergy);

      dataSharingService.logAutoSyncDateChange(ResourceTypeEnum.ALLERGY,
          savedAllergy.getDemographicNo(), savedAllergy.getId(), savedAllergy.getAutoSyncDate(),
          request);
      return savedAllergy;
    }

    /**
     * Creates a new prescription with new drugs associated with it, after validating data and setting calculated
     * fields
     * @param newPrescription the new prescription to be saved
     * @return The saved prescription
     */
    public Prescription saveNewPrescription(Prescription newPrescription) {
      newPrescription = preparePrescriptionForSave(newPrescription);
      return savePrescription(newPrescription);
    }

    /**
     * Saves a prescription to the database, old or new
     * 
     * @param prescriptionToSave the prescription to be saved
     * @return The saved prescription
     */
    public Prescription savePrescription(Prescription prescriptionToSave) {
        Prescription prescription = prescriptionRepository.save(prescriptionToSave);
        StringBuilder drugList = new StringBuilder();
        for (Drug drug : prescription.getItems()) {
            if (drug.getPriorRxRefId() != null && StringUtils.isNumeric(drug.getPriorRxRefId())) {
                Drug represcribedDrug = drugRepository.findOne(Integer.parseInt(drug.getPriorRxRefId()));
                if (represcribedDrug != null) {
                    represcribedDrug.archiveDrug(DrugArchiveReason.REPRESCRIBED);
                    represcribedDrug.setLastUpdateDate(new Date());
                    drugRepository.save(represcribedDrug);
                }
            }
            if (drug.getControlledSubstanceInfo() != null) {
                drug.getControlledSubstanceInfo().setDrug(drug);
            }
            drug.setScriptNo(prescription.getScriptNo());
            Date autoSyncDate = dataSharingService.calculateAutoSyncDate(drug.getCreateDate(), ResourceTypeEnum.MEDICATION,
                    drug.getDemographicNo());
            drug.setAutoSyncDate(autoSyncDate);

            Drug savedDrug = drugRepository.save(drug);
            dataSharingService.logAutoSyncDateChange(ResourceTypeEnum.MEDICATION, savedDrug.getDemographicNo(),
                    savedDrug.getId(), savedDrug.getAutoSyncDate(), prescriptionToSave.getProvider());
            drugList.append(drug.getSpecial()).append("\n");
            
            if (StringUtils.isNotEmpty(savedDrug.getPriorRxRefId())) {
                logService.writeEntry("represcribe",
                    "medication",
                    savedDrug.getId().toString(),
                    savedDrug.getDemographicNo(),
                    savedDrug.getSpecial(),
                    prescription.getProvider());
            }
        }
        logService.writeEntry("add",
            "prescription",
            prescription.getScriptNo().toString(),
            prescription.getDemographicNo(),
            drugList.toString(),
            prescription.getProvider());
        return prescription;
    }

    /**
     * Prepares a new prescription for save
     * @param newPrescription a new prescription, with one or more drugs
     * @return a new prescription ready for save
     */
    public Prescription preparePrescriptionForSave(Prescription newPrescription) {
        Provider provider = providerService.getProvider(newPrescription.getProviderNo());
        newPrescription.setProviderName(provider != null ? providerService.getProviderNameSignature(provider) : "");
        newPrescription.setDatePrescribed(new Date());
        newPrescription.setDatePrinted(new Date());
        newPrescription.setTextView(createPrescriptionTextView(newPrescription));
        newPrescription.setLastUpdateDate(new Date());
        newPrescription.setItems(newPrescription.getItems());

        for (Drug drug : newPrescription.getItems()) {

            drug.setProviderNo(newPrescription.getProviderNo());
            drug.setDemographicNo(newPrescription.getDemographicNo());

            drug.setEndDate(createEndDate(drug));
            // If the entered written date is null, uses the current date
            if (drug.getWrittenDate() == null) {
                drug.setWrittenDate(new Date());
            }

            if (drug.getTakeMin() > drug.getTakeMax()) {
                drug.setTakeMax(drug.getTakeMin());
            }

            if (drug.getQuantityUnit() != null) {
                drug.setDispensingUnits(drug.getQuantityUnit());
            }

            if (drug.getUnit() != null && drug.getUnit().contains("/")) {
                drug.setUnit(drug.getUnit().split("/")[0]);
            }

            drug.setSpecial(createSpecial(drug));
            drug.setCreateDate(new Date());
            drug.setLastUpdateDate(new Date());
            if (drug.getControlledSubstanceInfo() != null) {
                String patientIdType = drug.getControlledSubstanceInfo().getPatientIdentificationType();
                String patientIdNumber = drug.getControlledSubstanceInfo().getPatientIdentificationNumber();
                String providerPractitionerNumber = drug.getControlledSubstanceInfo().getProviderPractitionerNumber();

                if (StringUtils.isBlank(patientIdType) || !DemographicIdentificationTypesService.getIdentificationTypes().contains(patientIdType)) {
                    throw new IllegalArgumentException("Prescribing a controlled substance requires a valid patient id type.");
                }
                if (StringUtils.isBlank(patientIdNumber)) {
                    throw new IllegalArgumentException("Prescribing a controlled substance requires a valid patient id.");
                }
                if (StringUtils.isBlank(providerPractitionerNumber) || !providerPractitionerNumber.equals(newPrescription.getProvider().getPractitionerNo())) {
                    throw new IllegalArgumentException("Prescribing a controlled substance requires a valid provider practitioner number.");
                }
            }

        }

        return newPrescription;
    }
    
    public Drug createRePrescribeDrug(Drug oldDrug) {
        Drug newDrug = new Drug();
        newDrug.setupReprescribeValuesForNewDrug(oldDrug);
        
        String quantityStr = newDrug.getQuantity();
        if (quantityStr == null || !RxUtils.stringIsNumber(quantityStr)) {
            newDrug.setQuantity(RxUtils.getQuantityFromQuantityText(quantityStr));
            newDrug.setUnitName(RxUtils.getUnitNameFromQuantityText(quantityStr));
        }

        String pharmacyInstructions = StringUtils.trimToEmpty(newDrug.getPharmacyInstructions());
        if (StringUtils.isNotEmpty(oldDrug.getSpecialInstruction())) {
            pharmacyInstructions = oldDrug.getSpecialInstruction() + "\n" + pharmacyInstructions;
            // Removes the special instruction from the special field
            newDrug.setSpecial(newDrug.getSpecial().replaceAll(System.lineSeparator() + oldDrug.getSpecialInstruction(), ""));
        }
        newDrug.setPharmacyInstructions(pharmacyInstructions.trim());

        Drug lastDiscontinuedDrugForDemo = drugRepository.findLatestArchivedForDemographicByRegionalIdentifierAndAtc(
                oldDrug.getDemographicNo(), oldDrug.getRegionalIdentifier(), oldDrug.getAtc());
        if (lastDiscontinuedDrugForDemo != null && lastDiscontinuedDrugForDemo.getDiscontinued() &&
                !DrugArchiveReason.REPRESCRIBED.equals(lastDiscontinuedDrugForDemo.getArchivedReason())) {
            newDrug.archiveDrug(oldDrug.getArchivedReason(), oldDrug.getArchivedDate());
        }

        return newDrug;
    }

  public FaxStatusInternal faxPrescription(Prescription prescription, String faxLine,
      Printable.SignatureMethod signatureMethod, String signatureId, Provider printingProvider,
      boolean printWithPharmacyInfo, Integer selectedPharmacyId, String siteId, boolean isReprint)
      throws FaxException {
    var destinationPharmacy = prescription.getDestinationPharmacy();

    if ((destinationPharmacy == null) && (selectedPharmacyId != null)) {
      destinationPharmacy = pharmacyInfoRepository.findOne(selectedPharmacyId);
    }

    if (destinationPharmacy == null) {
      return FaxStatusInternal.ERROR;
    }

    val destination = destinationPharmacy.getFax().replaceAll("[()-]", "");
    prescriptionPdfCreator.init(prescription, signatureMethod, printWithPharmacyInfo,
        selectedPharmacyId, siteId, signatureId, printingProvider, false, isReprint, null);
    val watermark = getRxWatermark(prescription, siteId);
    return faxService.faxPdf(prescriptionPdfCreator, PageSize.LETTER, prescription.getProviderNo(),
        watermark, prescription.getDemographicNo(), faxLine, destination, FaxFileType.PRESCRIPTION);
  }

    /**
     * Creates a String from the provided {@link Drug} for its special text
     * @param d drun to create special text from
     * @return the special text created for the drug
     */
    public static String createSpecial(Drug d) {
        String special;
        if (d.getIsCustom()) {
            // Do custom specific stuff
            special = StringUtils.trimToEmpty(d.getCustomName());
        } else {
            special = StringUtils.trimToEmpty(d.getBrandName());
        }
        if (StringUtils.isEmpty(d.getSpecial().trim())) {
            RxInstructionParser parser = (RxInstructionParser) InstructionParser.getDefaultInstructionParser();
            d.setSpecial(parser.buildInstructionsFromDiscreteElements(d));
        }
        special += "\n" + d.getSpecial()
          + "\n"
          + "Qty:"
          + d.getQuantity()
          + " "
          + StringUtils.trimToEmpty(d.getDispensingUnits())
          + " Repeats:"
          + d.getRepeat();

        return special;
    }

    /**
     * Creates a textual view of the prescription that can be used to populate
     * the {@link Prescription} object's textView field.
     * @param prescription the prescription to create a summary of
     * @return a text summary of the provided prescription
     */
    private String createPrescriptionTextView(Prescription prescription) {
        SimpleDateFormat sdf = new SimpleDateFormat("MMMM d, yyyy");
        StringBuilder sb = new StringBuilder();

        Provider prescribingProvider = prescription.getProvider();
        Demographic demographic = prescription.getDemographic();
        String providerName = providerService.getProviderNameSignature(prescribingProvider);
        Clinic providerClinic = clinicService.getProviderClinic(prescribingProvider);

        sb.append(providerName).append("\n");
        sb.append(providerClinic.getName()).append("\n");
        sb.append(providerClinic.getAddress()).append("\n");
        sb.append(providerClinic.getCity()).append("\n");
        sb.append(providerClinic.getPostalCode()).append("\n");
        sb.append(providerClinic.getPhone()).append("\n");
        sb.append(providerClinic.getFax()).append("\n");
        sb.append(demographic.getFirstName()).append(" ").append(demographic.getLastName()).append("\n");
        sb.append(demographic.getAddress()).append("\n");
        sb.append(demographic.getCity()).append(" ").append(demographic.getPostal()).append("\n");
        sb.append(demographic.getPhone()).append("\n");
        sb.append(sdf.format(prescription.getDatePrescribed())).append("\n");

        for (Drug d : prescription.getItems()) {
            if (!StringUtils.isBlank(d.getSpecial())) {
                sb.append("\n");
                sb.append(createSpecial(d).replaceAll("\r\n", "\n"));
            }
        }

        return sb.toString();
    }

    /**
     * Calculates and returns the {@link Drug#endDate} for the provided drug from its
     * {@link Drug#duration}, {@link Drug#durationUnit}, and {@link Drug#rxDate}
     * @param drug The drug to calculate the endDate from
     * @return the date the drug will will be used up at
     */
    private Date createEndDate(Drug drug) {
        if (drug.getRxDate() == null) {
            drug.setRxDate(new Date());
        }
        String durationUnit = drug.getDurationUnit();
        Integer duration = NumberUtils.toInt(drug.getDuration());
        if (duration < 0) { duration = 0; }
        Integer repeatMult = (drug.getRepeat() > 0) ? drug.getRepeat() + 1 : 1;
        Integer days = duration;
        if ("D".equalsIgnoreCase(durationUnit)) {
            days = duration * repeatMult;
        } else if ("W".equalsIgnoreCase(durationUnit)) {
            days = duration * 7 * repeatMult;
        } else if ("M".equalsIgnoreCase(durationUnit)) {
            days = duration * 30 * repeatMult;
        }
        LocalDate endDate = drug.getRxDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        // if days was somehow less than 1 we don't want to add anything
        if (days > 0) {
            endDate = endDate.plus(days, ChronoUnit.DAYS);
        }
        // convert back to date object
        return Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    /**
     *  This function will take set a new preferred order index to a pharmacy,
     *  then adjust all other ones accordingly to the shift.
     * @param demographicNo Patients identification number
     * @param pharmacyId The ID relating to the pharmacy
     * @param preferredOrder The newly set preferred order of the pharmacy object
     * @return Will return the DemographicPharmacy object with the newly updated preferred order
     */
    public DemographicPharmacy setPreferredPharmacy (Integer demographicNo, Integer pharmacyId, Integer preferredOrder) {

        int currentOrder;
        List<DemographicPharmacy> demoPharmacyRecords = demographicPharmacyRepository.getAllByStatusAndDemographicNoAndPharmacyId(DemographicPharmacy.ACTIVE, demographicNo, pharmacyId);
        List<DemographicPharmacy> pharmacyOrderRange;

        if (!demoPharmacyRecords.isEmpty()) {
            int min, max;
            currentOrder = demoPharmacyRecords.get(0).getPreferredOrder();
            min = currentOrder > preferredOrder ? preferredOrder : currentOrder;
            max = currentOrder > preferredOrder ? currentOrder : preferredOrder;

            pharmacyOrderRange = demographicPharmacyRepository.getAllPharmaciesBetween(DemographicPharmacy.ACTIVE, demographicNo, min, max);
        } else {
            pharmacyOrderRange = demographicPharmacyRepository.getAllPharmaciesGreaterThanOrEqual(DemographicPharmacy.ACTIVE, demographicNo, preferredOrder);
        }

        for (DemographicPharmacy pharmacy : pharmacyOrderRange) {
            currentOrder = pharmacy.getPreferredOrder();
            currentOrder++;

            if (currentOrder > 10) {
                pharmacy.setStatus(DemographicPharmacy.INACTIVE);
            }

            pharmacy.setPreferredOrder(currentOrder);
            demographicPharmacyRepository.save(pharmacy);
        }

        if (demoPharmacyRecords.isEmpty()) {
            DemographicPharmacy demoPharmacyRecord = new DemographicPharmacy();
            demoPharmacyRecord.setAddDate(new Date());
            demoPharmacyRecord.setStatus(DemographicPharmacy.ACTIVE);
            demoPharmacyRecord.setDemographicNo(demographicNo);
            demoPharmacyRecord.setPharmacyId(pharmacyId);
            demoPharmacyRecord.setPreferredOrder(preferredOrder);
            demographicPharmacyRepository.save(demoPharmacyRecord);
            return demoPharmacyRecord;
        } else {
            demoPharmacyRecords.forEach(record -> record.setPreferredOrder(preferredOrder));
            demographicPharmacyRepository.save(demoPharmacyRecords);
            return demoPharmacyRecords.get(0);
        }
    }

    public List<Drug> getAllRxItemsByDemographicNumber(Integer demographicNumber) {

      List<Drug> demographicDrugs = drugRepository.findAllByDemographicNo(demographicNumber);
      List<Task> demographicTasks = taskRepository.findAllByDemographicNo(demographicNumber);

      boolean indivicareLinkEnabled =
          oscarProperties.getBooleanProperty("indivicare_link_enabled");

      for (Drug drug : demographicDrugs) {
        Prescription p = new Prescription();
        if (drug.getScriptNo() != null) {
          p = prescriptionRepository.findOne(drug.getScriptNo());
        }
        // Get all tasks by demographic no and set the hasTask variable to true if they exist.
        for (Task task : demographicTasks) {
          if (drug.getId().equals(task.getDrugId()) && task.getType().equals("p160-m")) {
            drug.setHasTask(true);
          }
        }

        if (p != null) {
          drug.setDeliveryMethod(p.getDeliveryMethod());
          drug.setDestinationPharmacy(p.getDestinationPharmacy());
          drug.setErxDeferred(p.isErxDeferred());
          drug.setErxFailedToSend(p.isErxFailedToSend());

          if ("ERX".equals(p.getDeliveryMethod())) {
            // Get cancel request
            Task cancelRequest = null;
            List<Task> cancelRequests = taskRepository.findTasksByDrugIdAndTypeInOrderByCreateDateDesc(
                drug.getId(), Collections.singletonList("e140-m"));
            Task cancelResponse = null;
            if (cancelRequests != null && !cancelRequests.isEmpty()) {
              // get the most recent cancel request
              cancelRequest = cancelRequests.get(0);
              cancelResponse = taskRepository.findByDrugIdAndTypeIn(drug.getId(),
                  TaskService.RXTaskRequest.getCancelRequestResponseCodes());
            }
            CancelStatusDisplay cancelDisplay = new CancelStatusDisplay(cancelRequest,
                cancelResponse);

            List<Task> dispenseNotifications = taskRepository.findTasksByDrugIdAndTypeInOrderByCreateDateDesc(
                drug.getId(),
                Collections.singletonList(
                    TaskService.RXTaskRequest.DISPENSE_NOTIFICATION.getCode()));
            for (Task notificationResponse : dispenseNotifications) {
              Task matchingCancelNotification = taskRepository.findFirstByCancelDispenseTaskByDispenseTaskId(
                  notificationResponse.getId());
              if (matchingCancelNotification != null) {
                drug.getErxNotificationDisplay().getDispenses().add(
                    new ErxNotificationDisplay.DispenseNotification(notificationResponse,
                        matchingCancelNotification));
              } else {
                drug.getErxNotificationDisplay().getDispenses()
                    .add(new ErxNotificationDisplay.DispenseNotification(notificationResponse));
              }
            }

            //The interaction response notifications can either be linked to a drug's prescription or an associated task
            //So we need to search both tables in case it's linked to one table but not the other. Sometimes it's
            //possible for an interaction response to have two links to the same error, and is stored in both tables.
            List<ErxInteractionResponse> interactionResponseNotifications = erxInteractionResponseRepository.findErxInteractionResponsesByScriptNo(
                drug.getScriptNo());
            List<ErxInteractionResponse> interactionResponseNotificationsFromDrugId = erxInteractionResponseRepository.findErxInteractionResponseByDrugId(
                drug.getId());

            //First we need to merge the two lists without duplicates
            for (ErxInteractionResponse notification : interactionResponseNotificationsFromDrugId) {
              if (!interactionResponseNotifications.contains(notification) &&
                  (cancelRequest == null || notification.getResponseToHeaderUuid()
                      .equals(cancelRequest.getMessageHeader().getUuid()))) {
                interactionResponseNotifications.add(notification);
              }
            }

            //Next we need to cycle through the combined list and check for any 999 errors
            //If a 999 error is found, that means that an ASYNCHONOUS_REJECTION_ERROR occured
            //and that the response did not make it back properly. In this case we need
            //to reset the cancel status so that a cancel attempt can be made again.
            for (ErxInteractionResponse notification : interactionResponseNotifications) {
              drug.getErxNotificationDisplay().getSendingErrors()
                  .add(new ErxNotificationDisplay.Notification(notification));
              if (cancelResponse == null && notification.getMessageHeader().getEventCode()
                  .equals("999")) {
                if (cancelRequest != null && notification.getResponseToHeaderUuid()
                    .equals(cancelRequest.getMessageHeader().getUuid())) {
                  cancelDisplay.setStatus("ERROR_999");
                } else if (!"ERROR_999".equals(cancelDisplay.getStatus())) {
                  // if not already flagged as an error; non-error
                  cancelDisplay.setStatus("NONE");
                }
              }
            }

            drug.setCancelStatus(cancelDisplay);
          }
          if (ClinicConfig.isMultisiteEnabled() && p.getSiteId() != null) {
            drug.setSiteName(siteRepository.getSiteBySiteId(p.getSiteId()).getName());
          }
        }

        if (indivicareLinkEnabled && org.apache.commons.lang.StringUtils.isNotBlank(
            drug.getRemoteProviderNo())) {
          drug.setRemoteDrug(true);
        }
      }
      return demographicDrugs;
    }

  public List<Drug> getAllRxItemsByDemographicNumberOptimized(final Integer demographicNumber) {

    List<Drug> demographicDrugs = drugRepository.findAllByDemographicNo(demographicNumber);
    // get matching prescriptions
    val prescriptions = prescriptionRepository.findAllByScriptNoInAsMap(
        demographicDrugs.stream().map(Drug::getScriptNo).collect(Collectors.toList()));

    // get site info for drugs
    val sites = siteRepository.findAllBySiteIdInAsMap(
        prescriptions.values().stream().map(Prescription::getSiteId)
            .filter(Objects::nonNull).collect(Collectors.toList()));

    // get all tasks for the found drugs
    val tasks = taskRepository.findByDemographicNoAndDrugIdIn(demographicNumber,
        demographicDrugs.stream().map(Drug::getId).collect(Collectors.toList()));

    // get matching erx cancel tasks (have "e140-m" type) and create a map of them by drug id
    val demographicCancelRequestTasksByDrugId = tasks.stream()
        .filter(task -> task.getType().equals(CANCEL.getCode()))
        .collect(Collectors.groupingBy(Task::getDrugId));
    // get matching erx cancel response tasks (have getCancelRequestResponseCodes type) and create a map of them by drug id
    val demographicCancelResponseTasksByDrugId = tasks.stream()
        .filter(task -> TaskService.RXTaskRequest.getCancelRequestResponseCodes()
            .contains(task.getType()))
        .collect(Collectors.groupingBy(Task::getDrugId));

    // get matching erx dispense notification tasks (have "p200-m" type) and create a map of them by drug id
    val demographicDispenseRequestTasksByDrugId = tasks.stream()
        .filter(task -> task.getType().equals(
            TaskService.RXTaskRequest.DISPENSE_NOTIFICATION.getCode()))
        .collect(Collectors.groupingBy(Task::getDrugId));

    // get matching erx renewal tasks (have "p160-m" type) and create a map of them by drug id
    val demographicRenewalTasksByDrugId = tasks.stream()
        .filter(task -> task.getType().equals("p160-m"))
        .collect(Collectors.groupingBy(Task::getDrugId));

    boolean indivicareLinkEnabled =
        oscarProperties.getBooleanProperty("indivicare_link_enabled");

    for (Drug drug : demographicDrugs) {
      Prescription p = new Prescription();
      if (drug.getScriptNo() != null) {
        p = prescriptions.get(drug.getScriptNo());
      }
      // Get all tasks by demographic no and set the hasTask variable to true if they exist.
      if (demographicRenewalTasksByDrugId.containsKey(drug.getId())) {
        drug.setHasTask(true);
      }

      if (p != null) {
        drug.setDeliveryMethod(p.getDeliveryMethod());
        drug.setDestinationPharmacy(p.getDestinationPharmacy());
        drug.setErxDeferred(p.isErxDeferred());
        drug.setErxFailedToSend(p.isErxFailedToSend());

        if ("ERX".equals(p.getDeliveryMethod())) {

          // Get cancel request
          Task cancelRequest = null;
          val cancelRequests = demographicCancelRequestTasksByDrugId.get(drug.getId());
          Task cancelResponse = null;
          if (cancelRequests != null && !cancelRequests.isEmpty()) {
            // get the most recent cancel request
            cancelRequest = cancelRequests.get(0);
            // get the most recent cancel response
            if (demographicCancelResponseTasksByDrugId.get(drug.getId()) != null) {
              cancelResponse = demographicCancelResponseTasksByDrugId.get(drug.getId()).stream()
                  .findFirst().orElse(null);
            }
          }
          CancelStatusDisplay cancelDisplay = new CancelStatusDisplay(cancelRequest,
              cancelResponse);

          val dispenseNotifications = demographicDispenseRequestTasksByDrugId.get(drug.getId());
          for (Task notificationResponse : dispenseNotifications) {
            Task matchingCancelNotification = taskRepository.findFirstByCancelDispenseTaskByDispenseTaskId(
                notificationResponse.getId());
            if (matchingCancelNotification != null) {
              drug.getErxNotificationDisplay().getDispenses().add(
                  new ErxNotificationDisplay.DispenseNotification(notificationResponse,
                      matchingCancelNotification));
            } else {
              drug.getErxNotificationDisplay().getDispenses()
                  .add(new ErxNotificationDisplay.DispenseNotification(notificationResponse));
            }
          }

          //The interaction response notifications can either be linked to a drug's prescription or an associated task
          //So we need to search both tables in case it's linked to one table but not the other. Sometimes it's
          //possible for an interaction response to have two links to the same error, and is stored in both tables.
          List<ErxInteractionResponse> interactionResponseNotifications = erxInteractionResponseRepository.findErxInteractionResponsesByScriptNo(
              drug.getScriptNo());
          List<ErxInteractionResponse> interactionResponseNotificationsFromDrugId = erxInteractionResponseRepository.findErxInteractionResponseByDrugId(
              drug.getId());

          //First we need to merge the two lists without duplicates
          for (ErxInteractionResponse notification : interactionResponseNotificationsFromDrugId) {
            if (!interactionResponseNotifications.contains(notification) &&
                (cancelRequest == null || notification.getResponseToHeaderUuid()
                    .equals(cancelRequest.getMessageHeader().getUuid()))) {
              interactionResponseNotifications.add(notification);
            }
          }

          //Next we need to cycle through the combined list and check for any 999 errors
          //If a 999 error is found, that means that an ASYNCHONOUS_REJECTION_ERROR occured
          //and that the response did not make it back properly. In this case we need
          //to reset the cancel status so that a cancel attempt can be made again.
          for (ErxInteractionResponse notification : interactionResponseNotifications) {
            drug.getErxNotificationDisplay().getSendingErrors()
                .add(new ErxNotificationDisplay.Notification(notification));
            if (cancelResponse == null && notification.getMessageHeader().getEventCode()
                .equals("999")) {
              if (cancelRequest != null && notification.getResponseToHeaderUuid()
                  .equals(cancelRequest.getMessageHeader().getUuid())) {
                cancelDisplay.setStatus("ERROR_999");
              } else if (!"ERROR_999".equals(cancelDisplay.getStatus())) {
                // if not already flagged as an error; non-error
                cancelDisplay.setStatus("NONE");
              }
            }
          }

          drug.setCancelStatus(cancelDisplay);
        }
        if (ClinicConfig.isMultisiteEnabled() && p.getSiteId() != null) {
          drug.setSiteName(
              sites.get(p.getSiteId()).getName()); // Replace with group query by site number
        }
      }

      if (indivicareLinkEnabled && org.apache.commons.lang.StringUtils.isNotBlank(
          drug.getRemoteProviderNo())) {
        drug.setRemoteDrug(true);
      }
    }
    return demographicDrugs;
  }

    /**
     * Get all pharmacies where a demographic pharmacy record is linked.
     * @param demographicNo Patients identification number
     * @return A list of all pharmacies with demographics linked
     */
    public List<PharmacyInfo> getAllActiveByDemographic(Integer demographicNo) {
        List<PharmacyInfo> pharmacies = pharmacyInfoRepository.getAllWithDemographicLinked(demographicNo);
        // Filter out any duplicate pharmacies caused by duplicate records in demographicPharmacy
        return pharmacies.stream().distinct().collect(Collectors.toList());
    }

    /**
     * This function will remove a pharmacy from preferred status
     * @param demographicNo Patients identification number
     * @param pharmacyId The ID used to identify the pharmacy
     */
    public void removePreferredPharmacy(Integer demographicNo, Integer pharmacyId) {
        List<DemographicPharmacy> pharmaciesToDelete = demographicPharmacyRepository.getAllByStatusAndDemographicNoAndPharmacyId(DemographicPharmacy.ACTIVE, demographicNo, pharmacyId);

        if (!pharmaciesToDelete.isEmpty()) {
            pharmaciesToDelete.forEach(pharmacy -> pharmacy.setStatus(DemographicPharmacy.INACTIVE));
            demographicPharmacyRepository.save(pharmaciesToDelete);
            Integer deletedPosition = pharmaciesToDelete.get(0).getPreferredOrder();

            List<DemographicPharmacy> pharmacies = demographicPharmacyRepository.getAllPharmaciesGreaterThan(DemographicPharmacy.ACTIVE, demographicNo, deletedPosition);
            int preferredOrder;

            for (DemographicPharmacy pharm : pharmacies) {
                preferredOrder = pharm.getPreferredOrder();
                preferredOrder--;
                pharm.setPreferredOrder(preferredOrder);
                demographicPharmacyRepository.save(pharm);
            }
        }
    }

    /**
     * This function will set a pharmacy as 'deleted' and will no longer display in OSCAR or KAI-EMR
     * @param pharmacyId The ID used to identify the pharmacy
     */
    public void deletePharmacy(Integer pharmacyId) {

        List<DemographicPharmacy> demographicPharmacies = demographicPharmacyRepository.getAllByPharmacyId(pharmacyId);

        for (DemographicPharmacy demographicPharmacy : demographicPharmacies) {
            removePreferredPharmacy(demographicPharmacy.getDemographicNo(), demographicPharmacy.getPharmacyId());
        }

        PharmacyInfo pharmacyInfo = pharmacyInfoRepository.findOne(pharmacyId);
        if (pharmacyInfo != null) {
            pharmacyInfo.setStatus(PharmacyInfo.DELETED);
            pharmacyInfoRepository.save(pharmacyInfo);
        }
    }

    /**
     * This function updates the pharmacies info with all changes made on the front end
     * @param pharmacy Pharmacy object that will be used to update its existing record
     * @return Returns the pharmacy object after successfully updating - if pharmacy object passed is null, it will return null
     */
    public PharmacyInfo updatePharmacyInfo(PharmacyInfo pharmacy) {
        if (pharmacy != null) {
            pharmacy.setAddDate(new Date());
            if (pharmacy.getStatus() == null) {
                pharmacy.setStatus(PharmacyInfo.ACTIVE);
            }
            pharmacyInfoRepository.save(pharmacy);
            for (PharmacyServiceType service : pharmacy.getServices()) {
                service.setPharmacyId(pharmacy.getId());
            }
            pharmacyServiceTypeRepository.save(pharmacy.getServices());
        }
        return pharmacy;
    }

    List<PharmacyInfo> searchCcEnabledPharmaciesByName(String searchString) {
        return pharmacyInfoRepository.getCcEnabledPharmaciesByName("%" + searchString + "%");
    } 
    
    public void updateExternalPharmacy(PharmacyInfo pharmacy) {
        PharmacyInfo existingPharmacy = pharmacyInfoRepository.getByOrganizationId(pharmacy.getOrganizationId());

        if (existingPharmacy != null) {
            List<PharmacyServiceType> pharmacyServiceTypes = new ArrayList<>();

            PharmacyServiceType.Concept.getDefaultServiceTypeConcepts().forEach(
                    concept -> pharmacyServiceTypes.add(new PharmacyServiceType(existingPharmacy.getId(), concept))
            );

            pharmacyServiceTypeRepository.delete(pharmacyServiceTypes);

            pharmacy.setId(existingPharmacy.getId());
            for (PharmacyServiceType service : pharmacy.getServices()) {
                service.setPharmacyId(pharmacy.getId());
            }
            pharmacyServiceTypeRepository.save(pharmacy.getServices());
        }
        pharmacy.setAddDate(new Date());
        pharmacyInfoRepository.save(pharmacy);
    }

    /**
     * Updates the provided pharmacy, saving the service types before the pharmacyInfo in case they are new services
     * This should only be used to save already existing pharmacies
     * @param pharmacy The pharmacy to update in the system
     */
    public void updatePharmacy(PharmacyInfo pharmacy) {
        if (!pharmacy.getServices().isEmpty()) { 
            pharmacyServiceTypeRepository.save(pharmacy.getServices());
        }
        pharmacy.setAddDate(new Date());
        pharmacyInfoRepository.save(pharmacy);
    }

    Provider getProviderByIdentifier(String key, String identifier) {
        List<Property> externalIdentifier = propertyService.getProperties(key, identifier);
        if (externalIdentifier != null && externalIdentifier.size() > 0) {
            return externalIdentifier.get(0).getProvider();
        }
        return null;
    }

    /**
     * Saves the provided drug to the database
     * @param drug The drug to save
     */
    public void saveDrug(Drug drug) {
        drugRepository.save(drug);
    }

    List<Task> renewalRequestByDrugId(Integer drugId){
        return taskRepository.findAllByDrugIdAndType(drugId, TaskService.RXTaskRequest.CREATE_RENEWAL.getCode());
    }

    /**
     * Generates the prescription image and returns it as a BASE64 encoded string
     * @param signatureMethod The method with how the prescription is signed
     * @param printWithPharmacyInfo Decides if the pharmacy info is included in the image
     * @param selectedPharmacyId The id of the pharmacy the prescription is being sent to
     * @param pageSize The dimensions of the prescription image
     * @param siteId The id of the current site being used
     * @param prescription The prescription the image is made from
     * @param watermark The watermark added to the image. No watermark will be added if it's blank
     * @param imageFormat The format the image will be output in
     * @param scale The scale of the image size as a float
     * @return The prescription converted to an image as a BASE64 encoded string
     */
    public String getPrescriptionImage(Printable.SignatureMethod signatureMethod,
            boolean printWithPharmacyInfo, Integer selectedPharmacyId, String pageSize,
            String siteId, Prescription prescription, String watermark, String imageFormat,
            float scale) {
        if ("ERX".equals(prescription.getDeliveryMethod()) && !prescription.isErxDeferred()
                && !prescription.isErxFailedToSend()) {
            signatureMethod = Printable.SignatureMethod.REFERENCE_COPY;
        } else if (signatureMethod == Printable.SignatureMethod.REFERENCE_COPY
                && prescription.isErxFailedToSend()) {
            signatureMethod = Printable.SignatureMethod.NONE;
        }
        prescriptionPdfCreator.init(
            prescription,
            signatureMethod,
            printWithPharmacyInfo,
            selectedPharmacyId,
            siteId,
            null,
            prescription.getProvider(),
            false,
            false,
            null
        );
        PDDocument document = prescriptionPdfCreator.generateDocument(
            PageSize.getRectangle(pageSize));
        if (StringUtils.isNotEmpty(watermark)) {
            PdfUtils.addWatermarks(document, watermark, PageSize.getRectangle(pageSize));
        }
        byte[] imageBytes = PdfUtils.getImageBytes(document, scale, imageFormat);
        return Base64.getEncoder().encodeToString(imageBytes);
    }

    public Drug parseInstructions(Drug oldDrug, Drug newDrug) {
        if (oldDrug.getParseInstructions() == false){
            // If the oldDrug has parseInstructions off, don't parse instructions
            // Copy over fields manually
            newDrug.setTakeMin(oldDrug.getTakeMin());
            newDrug.setTakeMax(oldDrug.getTakeMax());
            newDrug.setMethod(oldDrug.getMethod());
            newDrug.setDrugForm(oldDrug.getDrugForm());
            newDrug.setFreqCode(oldDrug.getFreqCode());
            newDrug.setDuration(oldDrug.getDuration());
            newDrug.setDurationUnit(oldDrug.getDurationUnit());
            newDrug.setQuantity(oldDrug.getQuantity());
            newDrug.setQuantityUnit(oldDrug.getQuantityUnit());
            newDrug.setSpecial(oldDrug.getSpecial());
            return newDrug;
        }
        // Since parsedInstructions are not stored in the database,
        // we have to re-parse the instructions to copy them
        InstructionParser parser = InstructionParser.getDefaultInstructionParser();
        parser.setValuesFromInstructions(oldDrug);
        newDrug.setParsedInstructions(oldDrug.getParsedInstructions());
        newDrug.setOutsideProviderCheckbox(
            StringUtils.isNotEmpty(newDrug.getOutsideProviderName())
            || StringUtils.isNotEmpty(newDrug.getOutsideProviderOhip())
        );
        newDrug.setSpecial(oldDrug.getSpecial());
        parser.setValuesFromInstructions(newDrug);
        if (systemPreferenceService.readBooleanPreference("rx_parser_shadowing_enabled", false)) {
            try {
                compareProToClassicParser(newDrug);
            } catch (Exception e) {
                log.error("An error occurred while comparing Pro to Classic parser: " + e.getMessage());
            }
        }
        return newDrug;
    }

    public String getRxWatermark(final Prescription p, final String siteId) {
        val watermarkType = systemPreferenceService.readByName("rx_watermark");
        if ("providerName".equals(watermarkType)) {
            return p.getProviderName();
        } else if ("clinicName".equals(watermarkType)) {
            var isMultisite = oscarProperties.getBooleanProperty("multisites");
            Site selectedSite;
            if (isMultisite && StringUtils.isNotEmpty(siteId) && NumberUtils.isParsable(siteId)
                    && (selectedSite = siteRepository.findOne(Integer.parseInt(siteId))) != null) {
                return selectedSite.getFullName();
            } else {
                return clinicService.getProviderClinic(p.getProvider()).getName();
            }
        }
        return "";
    }

    public List<HashMap<String, String>> listPreviousInstructions(final Drug drug) {
      List<Object[]> instructionList = new ArrayList<>();
      if (drug.getIsCustom()) {
        val customName = drug.getCustomName();
        if (!StringUtils.isEmpty(customName)) {
          instructionList = drugRepository.getPreviousInstructionsForCustomDrug(customName);
        }
      } else {
        val regionalIdentifier = drug.getRegionalIdentifier();
        val brandName = drug.getBrandName();
        if (!StringUtils.isEmpty(regionalIdentifier)) {
          instructionList = drugRepository.getPreviousInstructionsForRegionalDrug(regionalIdentifier);
          if (instructionList.isEmpty()) {
            instructionList = drugRepository.getPreviousInstructionsForBrandDrug(brandName);
          }
        } else {
          if (!StringUtils.isEmpty(brandName)) {
            instructionList = drugRepository.getPreviousInstructionsForBrandDrug(brandName);
          }
        }
      }
      return parsePreviousInstructions(drug, instructionList);
    }

    private static List<HashMap<String, String>> parsePreviousInstructions(
        final Drug drug,
        final List<Object[]> instructionList
    ) {
      List<HashMap<String, String>> previousInstructions = new ArrayList<>();
      for (val instruction: instructionList) {
        val instructionMap = new HashMap<String, String>();
        instructionMap.put("instruction", String.valueOf(instruction[0]));
        instructionMap.put("pharmacy_instruction", String.valueOf(instruction[1]));
        instructionMap.put("special_instruction", String.valueOf(instruction[2]));
        previousInstructions.add(instructionMap);
      }
      previousInstructions = trimMedHistoryList(drug, previousInstructions);
      return previousInstructions;
    }

    private static List<HashMap<String, String>> trimMedHistoryList(
        final Drug drug,
        final List<HashMap<String, String>> previousInstructions
    ) {
      List<HashMap<String, String>> trimmedInstructions = new ArrayList<>();
      val customName = drug.getCustomName();
      val brandName = drug.getBrandName();
      if (previousInstructions != null && !previousInstructions.isEmpty()) {
        for (val instructionMap : previousInstructions) {
          var instruction = instructionMap.get("instruction");
          var pharmacyInstruction = instructionMap.get("pharmacy_instruction");
          var specialInstruction = instructionMap.get("special_instruction");
          if (!StringUtils.isEmpty(instruction)) {
            if (customName != null && !customName.equalsIgnoreCase("null")) {
              instruction = instruction.replace(customName, "");
            }
            if (brandName != null && !brandName.equalsIgnoreCase("null")) {
              instruction = instruction.replace(brandName, "");
            }
            if (pharmacyInstruction != null && !pharmacyInstruction.equalsIgnoreCase("null")) {
              instruction = instruction.replace(pharmacyInstruction, "");
            }
            if (specialInstruction != null && !specialInstruction.equalsIgnoreCase("null")) {
              instruction = instruction.replace(specialInstruction, "");
            }
          }
          instruction = StringUtils.trimToEmpty(instruction).replace("\n", "");
          pharmacyInstruction = StringUtils.trimToEmpty(pharmacyInstruction).replace("\n", "");
          if ("null".equalsIgnoreCase(instruction)) {
            instruction = "";
          }
          if ("null".equalsIgnoreCase(pharmacyInstruction)) {
            pharmacyInstruction = "";
          }
          if ("null".equalsIgnoreCase(specialInstruction)) {
            specialInstruction = "";
          }
          HashMap<String, String> trimmedInstructionMap = new HashMap<>();
          trimmedInstructionMap.put("instruction", removeQuantityMitteRepeat(instruction));
          trimmedInstructionMap.put("pharmacy_instruction", pharmacyInstruction);
          if (pharmacyInstruction.isEmpty()) {
            trimmedInstructionMap.put("pharmacy_instruction", specialInstruction);
          }
          trimmedInstructions.add(trimmedInstructionMap);
        }
        trimmedInstructions = commonUniqueMedHistory(trimmedInstructions);
      }
      return trimmedInstructions;
    }

    private static List<HashMap<String, String>> commonUniqueMedHistory(
        final List<HashMap<String, String>> trimmedInstructions
    ) {
      if (trimmedInstructions != null && !trimmedInstructions.isEmpty()) {
        HashMap<HashMap<String, String>, Integer> instructionMapCount = new HashMap<>();
        val uniqueInstructions = new ArrayList<HashMap<String, String>>();
        for (val instructionMap : trimmedInstructions) {
          if (instructionMapCount.containsKey(instructionMap)) {
            instructionMapCount.put(instructionMap, instructionMapCount.get(instructionMap) + 1);
          } else {
            instructionMapCount.put(instructionMap, 1);
          }
        }
        val counts = new ArrayList<>(instructionMapCount.values());
        Collections.sort(counts);
        for (int i = counts.size() - 1; i >= 0; i--) {
          val instructionMaps = instructionMapCount.keySet();
          for (val instructionMap : instructionMaps) {
            val count = instructionMapCount.get(instructionMap);
            if (count.equals(counts.get(i))) {
              uniqueInstructions.add(instructionMap);
              instructionMapCount.remove(instructionMap);
              break;
            }
          }
        }
        return uniqueInstructions;
      }
      return trimmedInstructions;
    }

    private static String removeQuantityMitteRepeat(String instruction) {
      instruction = removeQuantityFromInstruction(instruction);
      instruction = removeMitteFromInstruction(instruction);
      instruction = removeRepeatFromInstruction(instruction);
      return instruction;
    }

    private static String replaceRegex(
        final String string,
        final String regex,
        final String replacement
    ) {
      Pattern pattern;
      Matcher matcher;
      pattern = Pattern.compile(regex);
      matcher = pattern.matcher(string);
      return matcher.replaceAll(replacement);
    }

    private static String removeQuantityFromInstruction(final String instruction) {
      return replaceRegex(instruction, "Qty:\\s*[0-9]*\\.?[0-9]*\\s*\\w*", "");
    }

    private static String removeMitteFromInstruction(final String instruction) {
      return replaceRegex(instruction, "Mitte:\\s*[0-9]*\\.?[0-9]*\\s*\\w*", "");
    }

    private static String removeRepeatFromInstruction(final String instruction) {
      return replaceRegex(instruction, "Repeats:\\s*[0-9]*\\.?[0-9]*\\s*\\w*", "");
    }

    protected Date modifyMainWrittenDateForReprint(
        final Prescription p,
        final boolean isReprint,
        final Date mainWrittenDate
    ) {
      return isReprint ? p.getFirstWrittenDate() : mainWrittenDate;
    }

    public Drug archiveDrug(
        final Drug drug,
        final String reason,
        final String comment
    ) {
      drug.archiveDrug(reason);
      drug.setComment(comment);
      return drugRepository.save(drug);
    }

  /**
   * Compares parsed drug instructions from Oscar Pro and Oscar Classic.
   * Logs the drug details and any discrepancies found between the two sets of instructions.
   *
   * @param drug The drug entity containing the instruction details.
   * @throws IOException If there is an issue fetching the classic instructions.
   */
  public void compareProToClassicParser(@NonNull final Drug drug) throws IOException {
    val proInstructions = generateProInstructions(drug);
    rxParserLogger.logDrugDetails("Oscar Pro", proInstructions);
    if (drug.getSpecial() == null) {
      throw new IllegalArgumentException("RxParserLogger - RxDrug's special instruction cannot be null.");
    }
    val classicInstructions = fetchClassicInstructions(drug.getSpecial());
    rxParserLogger.logDrugDetails("Oscar Classic", classicInstructions);
    compareAndLogDiscrepancies(drug, classicInstructions);
  }

  /**
   * Generates a map of drug instructions for Oscar Pro.
   *
   * @param drug The drug entity.
   * @return A map containing drug instructions.
   */
  private Map<String, String> generateProInstructions(@NonNull final Drug drug) {
    val instructions = new HashMap<String, String>();
    instructions.put(METHOD_KEY, drug.getMethod());
    instructions.put(TAKEMIN_KEY, Float.toString(drug.getTakeMin()));
    instructions.put(TAKEMAX_KEY, Float.toString(drug.getTakeMax()));
    instructions.put(FREQCODE_KEY, drug.getFreqCode());
    instructions.put(DURATION_KEY, drug.getDuration());
    instructions.put(DURATION_UNIT_KEY, drug.getDurationUnit());
    instructions.put(QUANTITY_KEY, drug.getQuantity());
    return instructions;
  }

  /**
   * Fetches and parses drug instructions from Oscar Classic using a provided instruction string.
   *
   * @param instruction The drug instruction string.
   * @return A map containing the parsed drug instructions.
   * @throws IOException If there is an issue with the request or response parsing.
   */
  private Map<String, String> fetchClassicInstructions(@NonNull final String instruction) throws IOException {
    try {
      val endpointUrl = oscarProHttpService.getLocalApiUrlPath(PARSE_DETAILED_INSTRUCTIONS_URL,
              URLEncoder.encode(instruction, String.valueOf(StandardCharsets.UTF_8)));
      val httpClient = oscarProHttpClient.getHttpClient();
      val getOne = new HttpGet(endpointUrl);
      getOne.addHeader("Accept", "*/*");
      val response = httpClient.execute(getOne);
      if (response.getStatusLine().getStatusCode() == 200) {
        return parseResponseToMap(response.getEntity());
      } else {
        throw new IOException(
            "RxParserLogger - Failed to fetch classic instructions with status: " + response.getStatusLine().getStatusCode()
        );
      }
    } catch (ClientProtocolException e) {
      throw new IOException("RxParserLogger - Error executing the request due to protocol violation", e);
    } catch (UnsupportedEncodingException e) {
      throw new IOException("RxParserLogger - Error encoding the instruction string", e);
    }
  }

  /**
   * Parses a given HttpEntity object to a Map representation.
   *
   * @param entity The HTTP entity containing the JSON response.
   * @return A map representing the parsed JSON response.
   * @throws IOException If there's an issue parsing the JSON.
   */
  private Map<String, String> parseResponseToMap(@NonNull final HttpEntity entity) throws IOException {
    try {
      val jsonResponse = EntityUtils.toString(entity);
      val mapper = new ObjectMapper();
      return mapper.readValue(jsonResponse, Map.class);
    } catch (JsonProcessingException e) {
      throw new IOException("RxParserLogger - Error parsing the JSON response in Rx parser comparator", e);
    }
  }

  /**
   * Compares the drug instructions between Oscar Pro and Oscar Classic and logs any discrepancies.
   *
   * @param proDrug The drug entity from Oscar Pro.
   * @param classicInstructions The instructions map from Oscar Classic.
   */
  private void compareAndLogDiscrepancies(
      @NonNull final Drug proDrug,
      @NonNull final Map<String, String> classicInstructions
  ) {
    val isDiscrepancyFound =
        logDiscrepancyIfAny(METHOD_KEY, proDrug.getMethod(), classicInstructions.get(METHOD_KEY))
        || logDiscrepancyIfAny(TAKEMIN_KEY, Float.toString(proDrug.getTakeMin()), classicInstructions.get(TAKEMIN_KEY))
        || logDiscrepancyIfAny(TAKEMAX_KEY, Float.toString(proDrug.getTakeMax()), classicInstructions.get(TAKEMAX_KEY))
        || logDiscrepancyIfAny(FREQCODE_KEY, proDrug.getFreqCode(), classicInstructions.get(FREQCODE_KEY))
        || logDiscrepancyIfAny(DURATION_KEY, proDrug.getDuration(), classicInstructions.get(DURATION_KEY))
        || logDiscrepancyIfAny(DURATION_UNIT_KEY, proDrug.getDurationUnit(), classicInstructions.get(DURATION_UNIT_KEY))
        || logDiscrepancyIfAny(QUANTITY_KEY, proDrug.getQuantity(), classicInstructions.get(QUANTITY_KEY));

    if (!isDiscrepancyFound) {
      rxParserLogger.logInfo(
          String.format(
              "RxParserLogger - The instruction '%s' has consistent results between the classic parser and the pro parser.",
              proDrug.getSpecial()
          )
      );
    }
  }

  /**
   * Logs any discrepancies found between Oscar Pro and Oscar Classic values for a given key.
   *
   * @param key The instruction key being compared.
   * @param proValue The value from Oscar Pro.
   * @param classicValue The value from Oscar Classic.
   * @return A boolean indicating if a discrepancy was found (true) or not (false).
   */
  private boolean logDiscrepancyIfAny(
      @NonNull final String key,
      final String proValue,
      final String classicValue
  ) {
    if (proValue == null && classicValue == null) {
      return false;
    }
    if (proValue == null || classicValue == null || !proValue.equalsIgnoreCase(classicValue)) {
      rxParserLogger.logDiscrepancy(key, proValue, classicValue);
      return true;
    }
    return false;
  }

  public byte[] previewMedications(final List<Integer> noteIds) throws IOException {
    val drugs = drugRepository.findAllByIdInOrderByRxDateDescIdDesc(noteIds);
    return simplePrinter.createPreview(SimplePrinterUtils.createMedicationSimplePrinterData(drugs));
  }
}
