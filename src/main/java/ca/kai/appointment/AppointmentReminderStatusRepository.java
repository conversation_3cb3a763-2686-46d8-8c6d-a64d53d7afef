package ca.kai.appointment;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;

import java.util.List;

public interface AppointmentReminderStatusRepository extends CrudRepository<AppointmentReminderStatus, Integer>
{
    @Query(value = "select ars.* FROM appointment_reminder_status ars LEFT JOIN appointment_reminders ar ON ars.appt_reminder_id = ar.id LEFT JOIN appointment a ON a.appointment_no = ar.appointment_id WHERE ars.all_delivered = false AND a.appointment_date < DATE_ADD(CURDATE(), INTERVAL ?1 DAY) AND a.appointment_date >= CURDATE()", nativeQuery = true)
    List<AppointmentReminderStatus> getAllPendingRemindersWithinRange(int days);
    
    AppointmentReminderStatus getByApptReminderId(Integer apptReminderId);
}