package ca.kai.integration.yourcare.patientportal.registration;

import ca.kai.authentication.AuthenticationService;
import ca.kai.demographic.Demographic;
import ca.kai.demographic.DemographicExt;
import ca.kai.demographic.DemographicExtKey;
import ca.kai.demographic.DemographicExtRepository;
import ca.kai.demographic.DemographicRepository;
import ca.kai.demographic.DemographicRepositoryService;
import ca.kai.integration.yourcare.patientportal.Patient;
import ca.kai.integration.yourcare.patientportal.YourCareConstants;
import ca.kai.log.LogService;
import ca.kai.provider.Provider;
import ca.kai.OscarProperties;
import ca.kai.util.BadRequestException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@AllArgsConstructor
@Service
@Slf4j
public class PortalRegistrationService {

	@Autowired private final DemographicRepositoryService demographicRepositoryService;
	@Autowired private final DemographicRepository demographicRepository;
	@Autowired private final DemographicExtRepository demographicExtRepository;
	@Autowired private final Environment environment;
	@Autowired private final LogService logService;
	@Autowired private final OscarProperties oscarProperties;
	@Autowired private final PortalRegistrationRepository registerPatientPortalRepository;
	@Autowired private final RestTemplate restTemplate;

	/**
	 * Conform patient portal mapping
	 * @param mapping
	 * @param request
	 */
	public void confirmPatientPortalMapping(
			Patient mapping,
			HttpServletRequest request
	) {
		log.debug("Patient mapping details: " + mapping);

		if (mapping == null) {
			throw new BadRequestException("No mapping details provider");
		}

		if (mapping.getRequestId() == null || mapping.getRequestId().isEmpty()) {
			throw new BadRequestException("RequestId is a required parameter");
		}

		if (mapping.getPatientPortalUserId() == null || mapping.getPatientPortalUserId().isEmpty()) {
			throw new BadRequestException("patient portal user id is a required parameter");
		}

		if ((mapping.getEmail() == null || mapping.getEmail().isEmpty())
				&& (mapping.getPhone() == null || mapping.getPhone().isEmpty())
		) {
			throw new BadRequestException("Patient portal user email or phone number must be provided for verification");
		}

		PortalRegistration registerRequest =
				registerPatientPortalRepository.findOne(Integer.parseInt(mapping.getRequestId()));
		if (registerRequest == null) {
			throw new BadRequestException("Request with id: " + mapping.getRequestId() + " does not exist.");
		}
		val now = Calendar.getInstance();
		val expiry = Calendar.getInstance();
		expiry.setTime(registerRequest.getExpiryDate());
		if (expiry.after(now)) {
			val demographic = demographicRepository.findOne(registerRequest.getDemographicNumber());
			if (demographic == null) {
				throw new BadRequestException("Error: Cannot find patient with id: " + registerRequest.getDemographicNumber());
			}
			if (mapping.getEmail() != null
					&& !mapping.getEmail().isEmpty()
					&& !demographic.getEmail().equalsIgnoreCase(mapping.getEmail().trim())
			) {
				throw new BadRequestException("Error: Email does not match. Cannot confirm mapping");
			}
			if (mapping.getPhone() != null
					&& !mapping.getPhone().isEmpty()
					&& !verifyPhone(mapping.getPhone(), demographic)
			) {
				throw new BadRequestException("Error: Phone does not match. Cannot confirm mapping");
			}
			val portalUser =
					demographicRepository.getByPortalUserId(mapping.getPatientPortalUserId());
			if (portalUser != null
					&& !demographic.getDemographicNumber().equals(portalUser.getDemographicNumber())
			) {
				throw new BadRequestException("Error: Portal user " + mapping.getPatientPortalUserId() + " already mapped to a different patient.");
			}
			demographic.setPortalUserId(mapping.getPatientPortalUserId());
			demographicRepositoryService.saveDemographic(demographic);

			// create or update registered date time demographic ext
			createOrUpdateDemographicExt(
					request,
					demographic.getDemographicNumber(),
					DemographicExtKey.PATIENT_PORTAL_REGISTERED_DATE_TIME.getKey(),
					formatDateTime(new Date())
			);

			logService.writeEntry(
					"PatientPortalRegistrationService.confirmPatientPortalMapping",
					"mapping confirmed",
					null,
					demographic.getDemographicNumber(),
					request.getRequestURI(),
					null,
					request
			);
			registerPatientPortalRepository.delete(registerRequest);
		} else {
			throw new BadRequestException("The request has already expired. Request Id: " + mapping.getRequestId());
		}
	}

	/**
	 * Create patient portal mapping request
	 *
	 * @param mapping
	 *            patient mapping details
	 * @param request
	 *            http servlet request
	 */
	public void createPatientPortalMappingRequest(Patient mapping, HttpServletRequest request) {

		if (mapping == null) {
			throw new BadRequestException("Error: the patient portal mapping request is null");
		}

		if (!StringUtils.isNotBlank(mapping.getEmail())) {
			throw new BadRequestException("Error: A valid email address is required to connect a Tia Health account.");
		}

		if (!StringUtils.isNotBlank(mapping.getPhone())) {
			throw new BadRequestException("Error: A phone is required to connect a Tia Health account.");
		}

		// cleanup any existing requests for the user
		deletePendingRequestsForUser(mapping.getDemographicNumber());
		val register = new PortalRegistration();
		register.setDemographicNumber(mapping.getDemographicNumber());
		String expiryHours =
				environment.getProperty(
						YourCareConstants.YOURCARE_REGISTER_REQUEST_VALID_FOR_HOURS_PROPERTY_NAME);
		int expiry = YourCareConstants.EXPIRE_REQUEST_TIME_IN_HOURS;
		if (expiryHours != null && !expiryHours.isEmpty()) {
			expiry = Integer.parseInt(expiryHours);
		}
		val calendar = Calendar.getInstance();
		register.setCreatedDate(calendar.getTime());
		calendar.add(Calendar.HOUR_OF_DAY, expiry);
		register.setExpiryDate(calendar.getTime());
		val savedPortalRequest = registerPatientPortalRepository.save(register);

		// Remove the termination extensions, in case an old mapping was created and terminated
		removeDemographicTerminationExts(mapping.getDemographicNumber());
		// create or update request id demographic ext
		createOrUpdateDemographicExt(
				request,
				mapping.getDemographicNumber(),
				DemographicExtKey.PATIENT_PORTAL_REGISTRATION_REQUEST_ID.getKey(),
				Integer.toString(savedPortalRequest.getId())
		);

		// create or update requester demographic ext
		Provider provider = AuthenticationService.getAuthenticatedProviderFromRequest(request);
		if (provider != null) {
			createOrUpdateDemographicExt(
					request,
					mapping.getDemographicNumber(),
					DemographicExtKey.PATIENT_PORTAL_REQUESTER_NAME.getKey(),
					provider.getFormattedName()
			);
		}

		// create or update request last updated date demographic ext
		createOrUpdateDemographicExt(
				request,
				mapping.getDemographicNumber(),
				DemographicExtKey.PATIENT_PORTAL_LAST_REQUEST_DATE_TIME.getKey(),
				formatDateTime(new Date())
		);

		// invoke Tia Health create patient mapping service
		mapping.setRequestId(Integer.toString(savedPortalRequest.getId()));
		invokeTiaHealthCreatePatientMapping(mapping);

		// audit log
		logService.writeEntry(
				"PatientPortalRegistrationService.createPatientPortalMappingRequest",
				"mapping created",
				Integer.toString(savedPortalRequest.getId()),
				mapping.getDemographicNumber(),
				request.getRequestURI(),
				null,
				request
		);
	}

	public void deletePatientPortalMapping(
			int demographicNumber,
			String terminationReason,
			HttpServletRequest request
	) throws BadRequestException {
		val demographic = demographicRepository.findOne(demographicNumber);
		if (demographic == null) {
			throw new BadRequestException("Demographic with id " + demographicNumber + " does not exist.");
		}
		val portalId = demographic.getPortalUserId();
		demographic.setPortalUserId(null);
		demographicRepositoryService.saveDemographic(demographic);
		deletePendingRequestsForUser(demographicNumber);
		removeDemographicExt(demographicNumber);

		val provider = AuthenticationService.getAuthenticatedProviderFromRequest(request);
		if (provider != null) {
			createOrUpdateDemographicExt(
					request,
					demographicNumber,
					DemographicExtKey.PATIENT_PORTAL_TERMINATOR_NAME.getKey(),
					provider.getFormattedName() != null ? provider.getFormattedName() : ""
			);
		}
		createOrUpdateDemographicExt(
				request,
				demographicNumber,
				DemographicExtKey.PATIENT_PORTAL_TERMINATION_DATE_TIME.getKey(),
				formatDateTime(new Date())
		);

		createOrUpdateDemographicExt(
				request,
				demographicNumber,
				DemographicExtKey.PATIENT_PORTAL_TERMINATION_REASON.getKey(),
				terminationReason
		);

		// invoke delete patient mapping service in Tia Health
		if (StringUtils.isNotBlank(portalId)) {
			invokeTiaHealthDeletePatientMapping(portalId);
		}
		// audit log
		logService.writeEntry(
				"PatientPortalRegistrationService.deletePatientPortalMapping",
				"portal user id" + portalId + "deleted",
				null,
				demographicNumber,
				request.getRequestURI(),
				null,
				request
		);
	}

	// Initiated by from Tia Health
	public void deletePatientPortalMapping(Patient mapping, HttpServletRequest request) {
		if (mapping == null) {
			throw new BadRequestException("No mapping details provider");
		}

		val mappingNo = mapping.getDemographicNumber();
		if (mappingNo <= 0) {
			throw new BadRequestException("Demographic No must be valid.");
		}

		if (mapping.getPatientPortalUserId() == null || mapping.getPatientPortalUserId().isEmpty()) {
			throw new BadRequestException("patient portal user id is a required parameter");
		}

		val demographic = demographicRepository.findOne(mappingNo);
		if (demographic == null) {
			throw new BadRequestException("Error: Demographic with id " + mappingNo + " does not exist.");
		}
		if (!mapping.getPatientPortalUserId().equals(demographic.getPortalUserId())) {
			throw new BadRequestException("Error: The patient portal Id does not match.");
		}

		verifyUserEmailAndPhone(mapping.getEmail(), mapping.getPhone(), demographic);

		demographic.setPortalUserId(null);
		demographicRepositoryService.saveDemographic(demographic);
		removeDemographicExt(mapping.getDemographicNumber());
		deletePendingRequestsForUser(mapping.getDemographicNumber());

		createOrUpdateDemographicExt(
				request,
				mapping.getDemographicNumber(),
				DemographicExtKey.PATIENT_PORTAL_TERMINATOR_NAME.getKey(),
				demographic.getFullFormattedName()
		);
		createOrUpdateDemographicExt(
				request,
				mapping.getDemographicNumber(),
				DemographicExtKey.PATIENT_PORTAL_TERMINATION_DATE_TIME.getKey(),
				formatDateTime(new Date())
		);

		var reason = YourCareConstants.DEFAULT_TERMINATED_REASON;
		if (mapping.getTerminationReason() != null && !mapping.getTerminationReason().isEmpty()) {
			reason = mapping.getTerminationReason();
		}
		createOrUpdateDemographicExt(request, mapping.getDemographicNumber(),
				DemographicExtKey.PATIENT_PORTAL_TERMINATION_REASON.getKey(), reason);

		logService.writeEntry(
				"PatientPortalRegistrationService.deleteMappingFromTiaHealth",
				"portal user id" + mapping.getPatientPortalUserId() + "deleted",
				null, mapping.getDemographicNumber(),
				request.getRequestURI(),
				null,
				request
		);
	}

	public void deleteAllPatientPortalMappings(String terminationReason, HttpServletRequest request) {

		val demographics = demographicRepository.getByPortalUserIdNotNull();
		val provider = AuthenticationService.getAuthenticatedProviderFromRequest(request);
		val terminatedDate = formatDateTime(new Date());
		for (Demographic demographic : demographics) {
			demographic.setPortalUserId(null);

			if (provider != null) {
				createOrUpdateDemographicExt(request, demographic.getDemographicNumber(),
						DemographicExtKey.PATIENT_PORTAL_TERMINATOR_NAME.getKey(), provider.getFormattedName());
			}
			createOrUpdateDemographicExt(request, demographic.getDemographicNumber(),
					DemographicExtKey.PATIENT_PORTAL_TERMINATION_DATE_TIME.getKey(), terminatedDate);

			createOrUpdateDemographicExt(request, demographic.getDemographicNumber(),
					DemographicExtKey.PATIENT_PORTAL_TERMINATION_REASON.getKey(), terminationReason);

		}
		demographicRepository.save(demographics);
		removeAllPatientPortalDemographicExt();
		// remove all pending requests
		registerPatientPortalRepository.deleteAll();
		logService.writeEntry("PatientPortalRegistrationService.deleteAllPatientPortalMappings", "Delete all mappings",
				null, null, request.getRequestURI(), null, request);
	}

	public List<PortalRegistration> getPendingRequestsForPatient(int demographicNo, HttpServletRequest request) {
		return registerPatientPortalRepository.getByDemographicNumberNotExpired(demographicNo);
	}

	// ------------------------------------------------------------- Private Methods

	/**
	 * Delete pending requests for patient with given demographic no
	 *
	 * @param demographicNo
	 *            patient demographic no
	 */
	private void deletePendingRequestsForUser(int demographicNo) {
		List<PortalRegistration> existingRequests = registerPatientPortalRepository
				.getByDemographicNumber(demographicNo);

		registerPatientPortalRepository.delete(existingRequests);
	}

	/**
	 * Verify phone number
	 *
	 * @param phone
	 * @param demographic
	 * @return
	 */
	private boolean verifyPhone(String phone, Demographic demographic) {
		if (phone == null || phone.isEmpty()) {
			return false;
		}
		phone = phone.trim();

		if (StringUtils.isNotBlank(demographic.getPhone())) {
			if (formatPhoneNumber(demographic.getPhone(), true)
					.equals(formatPhoneNumber(phone, false))) {
				return true;
			}
		}
		if (StringUtils.isNotBlank(demographic.getAlternativePhone())) {
			if (formatPhoneNumber(demographic.getAlternativePhone(), true)
					.equals(formatPhoneNumber(phone, false))) {
				return true;
			}
		}
		if (StringUtils.isNotBlank(demographic.getCellPhone())) {
			return formatPhoneNumber(demographic.getCellPhone(), true)
					.equals(formatPhoneNumber(phone, false));
		} else {
			val demoExt = getDemographicExt(
					demographic.getDemographicNumber(),
					DemographicExtKey.PHONE_CELL.getKey()
			);
			if (demoExt != null) {
				return formatPhoneNumber(demoExt.getValue(), true)
						.equals(formatPhoneNumber(phone, false));
			}
		}

		return false;
	}

	/**
	 * Verify user email and phone
	 *
	 * @param email
	 * @param phone
	 * @param demographic
	 */
	private void verifyUserEmailAndPhone(String email, String phone, Demographic demographic) {

		if (StringUtils.isBlank(email)) {
			throw new BadRequestException("Error: Email is null. Cannot remove mapping");
		}

		if (!email.trim().equalsIgnoreCase(demographic.getEmail().trim())) {
			throw new BadRequestException("Error: Email does not match.");
		}

		if (StringUtils.isBlank(phone)) {
			throw new BadRequestException("Error: Phone is null. Cannot delete mapping");
		}

		if (!verifyPhone(phone, demographic)) {
			throw new BadRequestException("Error: Phone does  not match.");
		}
	}

	/**
	 * Create or update demographic ext entry for patient
	 */
	private DemographicExt createOrUpdateDemographicExt(
			final HttpServletRequest request,
			final Integer demographicNo,
			final String key,
			final String value
	) {
		val provider = AuthenticationService.getAuthenticatedProviderFromRequest(request);
		var extension =
				demographicExtRepository.getFirstByKeyAndDemographicNumberOrderByDateTimeDesc(
						key,
						demographicNo
				);
		if (extension == null) {
			extension = new DemographicExt();
		}
		extension.setKey(key);
		extension.setDemographicNumber(demographicNo);
		if (provider != null) {
			extension.setProviderNo(provider.getProviderNo());
		}
		extension.setDateTime(new Date());
		extension.setValue(value);
		extension.setHidden('0');
		return demographicRepositoryService.saveDemographicExtension(extension);
	}

	/**
	 * Get demographic ext entry for patient
	 */
	private DemographicExt getDemographicExt(Integer demographicNo, String keyValue) {
		return demographicExtRepository.getFirstByKeyAndDemographicNumberOrderByDateTimeDesc(
				keyValue,
				demographicNo
		);
	}

	/**
	 * remove patient portal demographic ext
	 */
	private void removeDemographicExt(Integer demographicNo) {

		List<String> keys = new ArrayList<String>();
		keys.add(DemographicExtKey.PATIENT_PORTAL_REGISTRATION_REQUEST_ID.getKey());
		keys.add(DemographicExtKey.PATIENT_PORTAL_REQUESTER_NAME.getKey());
		keys.add(DemographicExtKey.PATIENT_PORTAL_LAST_REQUEST_DATE_TIME.getKey());
		keys.add(DemographicExtKey.PATIENT_PORTAL_REGISTERED_DATE_TIME.getKey());

		List<DemographicExt> extensions =
				demographicExtRepository.getAllByDemographicNumberAndKeyIn(demographicNo, keys);

		demographicExtRepository.delete(extensions);
	}

	private void removeDemographicTerminationExts(Integer demographicNo) {

		List<String> keys = new ArrayList<String>();
		keys.add(DemographicExtKey.PATIENT_PORTAL_TERMINATION_DATE_TIME.getKey());
		keys.add(DemographicExtKey.PATIENT_PORTAL_TERMINATION_REASON.getKey());
		keys.add(DemographicExtKey.PATIENT_PORTAL_TERMINATOR_NAME.getKey());

		List<DemographicExt> extensions =
				demographicExtRepository.getAllByDemographicNumberAndKeyIn(demographicNo, keys);

		demographicExtRepository.delete(extensions);
	}

	private void removeAllPatientPortalDemographicExt() {
		List<DemographicExt> regRequestIds = demographicExtRepository
				.getAllByKey(DemographicExtKey.PATIENT_PORTAL_REGISTRATION_REQUEST_ID.getKey());
		List<DemographicExt> requesterNames = demographicExtRepository
				.getAllByKey(DemographicExtKey.PATIENT_PORTAL_REQUESTER_NAME.getKey());
		List<DemographicExt> lastReqDateTime = demographicExtRepository
				.getAllByKey(DemographicExtKey.PATIENT_PORTAL_LAST_REQUEST_DATE_TIME.getKey());
		List<DemographicExt> registeredDateTime = demographicExtRepository
				.getAllByKey(DemographicExtKey.PATIENT_PORTAL_REGISTERED_DATE_TIME.getKey());
		demographicExtRepository.delete(regRequestIds);
		demographicExtRepository.delete(requesterNames);
		demographicExtRepository.delete(lastReqDateTime);
		demographicExtRepository.delete(registeredDateTime);

	}

	/**
	 * Format date time
	 */
	private String formatDateTime(Date date) {
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		return df.format(date);
	}

	/**
	 * Invoke create patient mapping service in Tia Health
	 */
	private void invokeTiaHealthCreatePatientMapping(Patient patient) {
		log.trace("invokeTiaHealthCreatePatientMapping");
		try {
			validatePropertySettings();

			String tiaHealthBaseUrl = getTiaHealthBaseUrl();
			String oscarBaseUrl = environment.getProperty(
					YourCareConstants.OSCAR_EMR_BASE_URL_PROPERTY_NAME);

			CreatePatientMappingRequest mappingRequest =
					new CreatePatientMappingRequest(patient, oscarBaseUrl);
			log.debug("create patient mapping request: \n" + mappingRequest);

			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_JSON);

			headers.add(
					oscarProperties.getProperty(YourCareConstants.YOURCARE_INTEGRATION_CLIENT_ID_NAME),
					oscarProperties.getProperty(YourCareConstants.YOURCARE_INTEGRATION_CLIENT_ID)
			);
			headers.add(
					oscarProperties.getProperty(YourCareConstants.YOURCARE_INTEGRATION_API_KEY_NAME),
					oscarProperties.getProperty(YourCareConstants.YOURCARE_INTEGRATION_API_KEY)
			);
			ParameterizedTypeReference<CreatePatientMappingResponse> typeRef =
					new ParameterizedTypeReference<CreatePatientMappingResponse>(){};

			UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(
					tiaHealthBaseUrl + YourCareConstants.YOURCARE_CREATE_PATIENT_MAPPING_URL
			);
			Map<String, Object> urlVariables = new HashMap<>();
			ResponseEntity<CreatePatientMappingResponse> response =
					restTemplate.exchange(
							builder.buildAndExpand(urlVariables).encode().toUri(),
							HttpMethod.POST,
							new HttpEntity<>(mappingRequest, headers),
							typeRef
					);

			CreatePatientMappingResponse mappingResponse = response.getBody();
			log.debug("create patient mapping response: " + mappingResponse);
		} catch (HttpStatusCodeException ex) {
			log.warn("Error creating patient mapping in Tia Health: " + ex.getResponseBodyAsString(), ex);
			throw ex;
		}
	}

	/**
	 * Invoke delete patient mapping service in Tia Health
	 */
	private void invokeTiaHealthDeletePatientMapping(String portalUserId) {
		log.trace("invokeTiaHealthDeletePatientMapping");
		try {

			validatePropertySettings();

			String tiaHealthBaseUrl = getTiaHealthBaseUrl();
			String oscarBaseUrl = environment.getProperty(
					YourCareConstants.OSCAR_EMR_BASE_URL_PROPERTY_NAME);

			DeletePatientMappingRequest mappingRequest =
					new DeletePatientMappingRequest(oscarBaseUrl, portalUserId);
			log.debug("delete patient mapping request: \n{}", mappingRequest);

			HttpHeaders headers = new HttpHeaders();
			headers.setContentType(MediaType.APPLICATION_JSON);
			headers.add(
					oscarProperties.getProperty(YourCareConstants.YOURCARE_INTEGRATION_CLIENT_ID_NAME),
					oscarProperties.getProperty(YourCareConstants.YOURCARE_INTEGRATION_CLIENT_ID)
			);
			headers.add(
					oscarProperties.getProperty(YourCareConstants.YOURCARE_INTEGRATION_API_KEY_NAME),
					oscarProperties.getProperty(YourCareConstants.YOURCARE_INTEGRATION_API_KEY)
			);

			UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(
					tiaHealthBaseUrl + YourCareConstants.YOURCARE_DELETE_PATIENT_MAPPING_URL
			);

			restTemplate.exchange(
					builder.buildAndExpand(new HashMap<>()).encode().toUri(),
					HttpMethod.POST,
					new HttpEntity<>(mappingRequest, headers),
					String.class
			);

		} catch (HttpStatusCodeException ex) {
			log.warn("Error deleting patient mapping in Tia Health: " + ex.getResponseBodyAsString(), ex);
			throw ex;
		}
	}

	/* HELPER METHODS */

	private String getTiaHealthBaseUrl() {
		String baseUrl = environment.getProperty(YourCareConstants.YOURCARE_BASE_URL_PROPERTY_NAME);
		if (baseUrl != null) {
			if (!baseUrl.endsWith("/")) {
				baseUrl = baseUrl + "/";
			}
		}
		return baseUrl;
	}

	private static String formatPhoneNumber(String phone, boolean ignoreFormatError) {
		if (phone == null || phone.trim().isEmpty()) {
			return null;
		}

		String formattedPhone = null;
		if (phone.startsWith("+")) {
			formattedPhone = phone.substring(1).replaceAll(YourCareConstants.PHONE_NUMBER_CLEAN_UP_PATTERN, "");
			formattedPhone = "+" + formattedPhone;
		} else {
			formattedPhone = phone.replaceAll(YourCareConstants.PHONE_NUMBER_CLEAN_UP_PATTERN, "");
		}

		log.debug("formattedPhone: " + formattedPhone);
		// only support North America phone # for now
		if (!formattedPhone.startsWith("+")) {
			if (formattedPhone.length() == 10) {
				formattedPhone = "+1" + formattedPhone;
			} else if (formattedPhone.length() == 7) {
				String msg = "Phone number " + phone + " does not include area code.";
				log.warn(msg);
				if (ignoreFormatError) {
					return formattedPhone;
				}
				throw new PortalRegistrationException(msg);
			} else {
				String msg = "Wrong number of digits in phone number: " + phone +
						". Expect 10 digits phone number including the area code.";
				log.warn(msg);
				if (ignoreFormatError) {
					return formattedPhone;
				}
				throw new PortalRegistrationException(msg);
			}
		}
		return formattedPhone;
	}

	private void validatePropertySettings() {
		validateOscarProperties();
		validateKaiEmrProperties();
	}

	private void validateOscarProperties() {
		validatePropertySetting(YourCareConstants.OSCAR_EMR_BASE_URL_PROPERTY_NAME);
		validatePropertySetting(YourCareConstants.YOURCARE_INTEGRATION_CLIENT_ID_NAME);
		validatePropertySetting(YourCareConstants.YOURCARE_INTEGRATION_CLIENT_ID);
		validatePropertySetting(YourCareConstants.YOURCARE_INTEGRATION_API_KEY_NAME);
		validatePropertySetting(YourCareConstants.YOURCARE_INTEGRATION_API_KEY);
	}

	private void validateKaiEmrProperties() {
		validateEnvironmentSetting(YourCareConstants.YOURCARE_BASE_URL_PROPERTY_NAME);
	}

	private void validateEnvironmentSetting(final String property) {
		validatePropertyValue("kaiemr.properties", environment.getProperty(property));
	}

	private void validatePropertySetting(final String property) {
		validatePropertyValue("oscar.properties", oscarProperties.getProperty(property));
	}

	private void validatePropertyValue(final String location, final String property) {
		if (StringUtils.isBlank(property)) {
			val error = String.format("'%s' missing from '%s'.", property, location);
			log.error(error);
			throw new PortalRegistrationException(error);
		}
	}

	/* STATIC CLASSES */

	public static class CreatePatientMappingRequest {
		@Getter @Setter private String firstName;
		@Getter @Setter private String lastName;
		@Getter @Setter private String email;
		@Getter @Setter private String phone;
		@Getter @Setter private String emrBaseUrl;
		@Getter @Setter private String requestId;
		@Getter @Setter private Integer demographicNo;

		public CreatePatientMappingRequest(Patient mappingDetails, String emrBaseUrl) {
			super();
			this.firstName = mappingDetails.getFirstName();
			this.lastName = mappingDetails.getLastName();
			this.email = mappingDetails.getEmail();
			this.phone = formatPhoneNumber(mappingDetails.getPhone(), false);
			this.emrBaseUrl = emrBaseUrl;
			this.requestId = mappingDetails.getRequestId();
			this.demographicNo = mappingDetails.getDemographicNumber();
		}
	}

	@ToString
	public static class CreatePatientMappingResponse {
		@Getter @Setter private String userId;
		@Getter @Setter private String invitationId;
	}

	@ToString
	public static class DeletePatientMappingRequest {
		@Getter @Setter private String emrBaseUrl;
		@Getter @Setter private String insigUserId;

		public DeletePatientMappingRequest(String emrBaseUrl, String insigUserId) {
			super();
			this.emrBaseUrl = emrBaseUrl;
			this.insigUserId = insigUserId;
		}
	}
}
