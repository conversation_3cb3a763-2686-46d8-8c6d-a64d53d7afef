package ca.kai.diseaseregistry;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.data.jpa.domain.Specification;
import ca.kai.demographic.SearchCriteria;
import ca.kai.utils.SpecificationUtils;

public class DxResearchSpecification implements Specification<DxResearch> {

  private Collection<SearchCriteria> searchCriteria;

  public DxResearchSpecification(Collection<SearchCriteria> searchCriteria) {
    this.searchCriteria = searchCriteria;
  }

  @Override
  public Predicate toPredicate(Root<DxResearch> root, CriteriaQuery<?> query,
      CriteriaBuilder builder) {
    SpecificationUtils.validateSearchCriteria(this.searchCriteria);
    List<Predicate> predicates = new ArrayList<Predicate>();
    for (SearchCriteria criteria : searchCriteria) {
      if (criteria.getValue() instanceof String) {
        predicates.add(SpecificationUtils.getQueryForStringField(root, query, builder, criteria));
      } else if (criteria.getValue() instanceof List<?>) {
        predicates.add(SpecificationUtils.getQueryForDateField(root, query, builder, criteria));
      } else if (criteria.getValue() instanceof Integer) {
        predicates.add(SpecificationUtils.getQueryForIntegerField(root, query, builder, criteria));
      } else {
        throw new IllegalArgumentException("Cannot search on field: " + criteria.getKey()
            + " with type: " + criteria.getValue().getClass().getSimpleName());
      }
    }
    return builder.and(predicates.toArray(new Predicate[0]));
  }
}
