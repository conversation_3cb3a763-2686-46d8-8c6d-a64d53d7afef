package ca.kai.billing.favourite;

import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.hibernate.annotations.Where;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GenerationType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Table(name="billing_on_favourite")
public class BillingFavourite {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    private String name;

    @Column(name="dx")
    private String dx = "";

    @Column(name="dx1")
    private String dx1 = "";

    @Column(name="dx2")
    private String dx2 = "";
    
    @Column(name="service_dx")
    private String serviceDx;

    @Column(name="provider_no")
    private String providerNo;

    @Temporal(TemporalType.TIMESTAMP)
    private Date timestamp;

    private int deleted = 0;

    private String visitType = null;

    private String location = null;

    @Column(name = "sli_code")
    private String sliCode = "";

    @OneToMany(fetch = FetchType.EAGER, cascade = CascadeType.ALL)
    @JoinColumn(name = "favourite_id", referencedColumnName = "id")
    @Where(clause = "deleted = false")
    @NotFound(action = NotFoundAction.IGNORE)
    private List<BillingFavouriteItem> billingItems = new ArrayList<BillingFavouriteItem>();

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDx() {
        return dx;
    }

    public void setDx(String dx) {
        this.dx = dx;
    }

    public String getDx1() {
        return dx1;
    }

    public void setDx1(String dx1) {
        this.dx1 = dx1;
    }

    public String getDx2() {
        return dx2;
    }

    public void setDx2(String dx2) {
        this.dx2 = dx2;
    }

    @Deprecated
    public String getServiceDx() {
        return serviceDx;
    }

    @Deprecated
    public void setServiceDx(String serviceDx) {
        this.serviceDx = serviceDx;
    }

    public String getProviderNo() {
        return providerNo;
    }

    public void setProviderNo(String providerNo) {
        this.providerNo = providerNo;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public int getDeleted() {
        return deleted;
    }

    public void setDeleted(int deleted) {
        this.deleted = deleted;
    }

    public String getVisitType() {
        return visitType;
    }

    public void setVisitType(String visitType) {
        this.visitType = visitType;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getSliCode() {
        return sliCode;
    }

    public void setSliCode(String sliCode) {
        this.sliCode = sliCode;
    }

    public List<BillingFavouriteItem> getBillingItems() {
        return billingItems;
    }

    public void setBillingItems(List<BillingFavouriteItem> billingItems) {
        this.billingItems = billingItems;
    }
}
