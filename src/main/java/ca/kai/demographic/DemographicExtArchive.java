package ca.kai.demographic;

import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.val;

@Entity
@NoArgsConstructor
@Table(name = "demographicExtArchive")
public class DemographicExtArchive implements Serializable, PatientData {
	private static final long serialVersionUID = -2981357879423093412L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Getter private Integer id;

	@Column(name = "archiveId")
	@Getter @Setter	private Integer archiveId;

	@Column(name = "demographic_no")
	@Getter @Setter private Integer demographicNumber;

	@Column(name = "provider_no")
	@Getter @Setter private String providerNo;

	@Column(name = "key_val")
	@Getter @Setter private String key;

	@Column(name = "date_time")
	@Temporal(TemporalType.TIMESTAMP)
	@Getter @Setter	private java.util.Date dateCreated;

	@Getter @Setter private String value;
	@Getter @Setter	private boolean hidden;

	public DemographicExtArchive(DemographicExt extension, Integer archiveId) {
		if (extension == null) {
			throw new IllegalArgumentException();
		}
		this.archiveId = archiveId;
		this.dateCreated = extension.getDateTime();
		this.demographicNumber = extension.getDemographicNumber();
		this.providerNo = extension.getProviderNo();
		this.key = extension.getKey();
		this.value = extension.getValue();
		this.hidden = Boolean.parseBoolean(String.valueOf(extension.getHidden()));
	}

	public DemographicExt asDemographicExt() {
		return new DemographicExt(this);
	}
}
