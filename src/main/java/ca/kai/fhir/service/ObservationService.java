package ca.kai.fhir.service;

import static ca.kai.demographic.SearchCriteria.OperationTypeEnum.EQUAL_TO;

import ca.kai.caseManagementNote.CaseManagementNote;
import ca.kai.caseManagementNote.CaseManagementNoteRepository;
import ca.kai.caseManagementNote.CaseManagementNoteSpecification;
import ca.kai.demographic.SearchCriteria;
import ca.kai.fhir.converter.CaseManagementNoteCode;
import ca.kai.fhir.converter.SocialHistoryMeasurement;
import ca.kai.measurement.Measurement;
import ca.kai.measurement.MeasurementRepository;
import java.util.Arrays;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.val;
import org.hl7.fhir.r4.model.codesystems.ObservationCategory;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ObservationService {

  private final MeasurementRepository measurementRepository;
  private final CaseManagementNoteRepository caseManagementNoteRepository;

  public List<Measurement> getSocialHistoryMeasurements(
      final boolean isForPatientOnly,
      final Integer demographicNo
  ) {
    if (isForPatientOnly) {
      return measurementRepository.findAvailableForSyncByDemographicNoAndTypeIn(
          demographicNo, SocialHistoryMeasurement.getAllTypes());
    } else {
      return measurementRepository.findAllByDemographicNoAndTypeIn(
          demographicNo, SocialHistoryMeasurement.getAllTypes());
    }
  }

  public List<CaseManagementNote> getSocialHistoryNotes(final Integer demographicNo) {
    val searchParams = Arrays.asList(
        new SearchCriteria("demographicNo", EQUAL_TO, demographicNo),
        new SearchCriteria(
            "issue.code",
            EQUAL_TO,
            CaseManagementNoteCode.getCodesForObservationCategory(ObservationCategory.SOCIALHISTORY)
        )
    );

    return caseManagementNoteRepository.findAll(new CaseManagementNoteSpecification(searchParams));
  }
}
