package ca.kai.fhir.converter.r4.extension;

public class AppointmentTypeExtensionUrls {
  
  public static final String DURATION = "https://apps.health/ValueSet/appointment-type/duration";
  public static final String ENABLED = "https://apps.health/ValueSet/appointment-type/enabled";
  public static final String LOCATION = "https://apps.health/ValueSet/appointment-type/location";
  public static final String REASON = "https://apps.health/ValueSet/appointment-type/reason";
  public static final String REASON_CODE = "https://apps.health/ValueSet/appointment-type/reason-code";
  public static final String RESOURCES = "https://apps.health/ValueSet/appointment-type/resources";

  private AppointmentTypeExtensionUrls() {
  }
}
