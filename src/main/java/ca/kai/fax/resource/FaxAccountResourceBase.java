package ca.kai.fax.resource;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Optional;

@Data
public abstract class FaxAccountResourceBase implements Serializable {

  @NotNull(message = "Fax Display Name is required")
  protected String displayName;
  protected boolean accountEnabled;
  protected boolean inboundEnabled;
  protected boolean outboundEnabled;
  protected String coverLetterOption;
  protected String inboxProviderId;

  @JsonIgnore
  public Optional<String> getInboxProviderIdOptional() {
    return Optional.ofNullable(inboxProviderId);
  }
}
