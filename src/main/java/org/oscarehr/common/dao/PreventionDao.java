/**
 *
 * Copyright (c) 2005-2012. Centre for Research on Inner City Health, St. Michael's Hospital, Toronto. All Rights Reserved.
 * This software is published under the GPL GNU General Public License.
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA 02111-1307, USA.
 *
 * This software was written for
 * Centre for Research on Inner City Health, St. Michael's Hospital,
 * Toronto, Ontario, Canada
 */
package org.oscarehr.common.dao;

import health.apps.gateway.converters.ImmunizationConverter;
import health.apps.gateway.service.GWConfigurationService;
import health.apps.gateway.service.GatewayDao;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.persistence.Query;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.hl7.fhir.r4.model.Immunization;
import org.oscarehr.common.NativeSql;
import org.oscarehr.common.enums.FeatureFlagEnum;
import org.oscarehr.common.model.Demographic;
import org.oscarehr.common.model.Prevention;
import org.oscarehr.managers.TicklerManager;
import org.oscarehr.sharingcenter.util.PreventionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

// Explicitly set bean name is due to PreventionMergedDemographicDao being given the same bean
// name. Required to autowire beans to this class and resolve ambiguity errors on startup.
@Repository("preventionDaoImpl")
@Slf4j
public class PreventionDao extends AbstractDao<Prevention> {

	@Autowired
	private GatewayDao gatewayDao;

	@Autowired
	private DemographicDao demographicDao;

	@Autowired
	private TicklerManager ticklerManager;

	@Autowired
	private GWConfigurationService gwConfigurationService;

	public PreventionDao() {
		super(Prevention.class);
	}

	public List<Prevention> findByDemographicId(Integer demographicId) {
		Query query = entityManager.createQuery("select x from "+modelClass.getSimpleName()+" x where demographicId=?1");
		query.setParameter(1, demographicId);

		List<Prevention> results = query.getResultList();

		return (results);
	}
    
	/**
	 * @return results ordered by lastUpdateDate
	 */
	public List<Prevention> findByUpdateDate(Date updatedAfterThisDateExclusive, int itemsToReturn) {
		String sqlCommand = "select x from "+modelClass.getSimpleName()+" x where x.lastUpdateDate>?1 order by x.lastUpdateDate";

		Query query = entityManager.createQuery(sqlCommand);
		query.setParameter(1, updatedAfterThisDateExclusive);
		setLimit(query, itemsToReturn);
		
		@SuppressWarnings("unchecked")
		List<Prevention> results = query.getResultList();
		return (results);
	}

    public List<Prevention> findByDemographicIdAfterDatetime(Integer demographicId, Date dateTime) {
    	Query query = entityManager.createQuery("select x from Prevention x where demographicId=?1 and lastUpdateDate>=?2 and deleted='0'");
    	query.setParameter(1, demographicId);
		query.setParameter(2, dateTime);

		@SuppressWarnings("unchecked")
        List<Prevention> results = query.getResultList();

		return (results);
	}

    public List<Prevention> findByDemographicIdAfterDatetimeExclusive(Integer demographicId, Date dateTime) {
    	Query query = entityManager.createQuery("select x from Prevention x where demographicId=?1 and lastUpdateDate>?2 and deleted='0'");
    	query.setParameter(1, demographicId);
		query.setParameter(2, dateTime);

		@SuppressWarnings("unchecked")
        List<Prevention> results = query.getResultList();

		return (results);
	}
    
	/*
	 * for integrator
	 */
	public List<Integer> findDemographicIdsAfterDatetime(Date dateTime) {
		Query query = entityManager.createQuery("select x.demographicId from Prevention x where x.lastUpdateDate > ?1");
		query.setParameter(1, dateTime);

		@SuppressWarnings("unchecked")
		List<Integer> results = query.getResultList();

		return (results);
	}
	
	public List<Prevention> findByProviderDemographicLastUpdateDate(String providerNo, Integer demographicId, Date updatedAfterThisDateExclusive, int itemsToReturn) {
		String sqlCommand = "select x from "+modelClass.getSimpleName()+" x where x.demographicId=:demographicId and x.providerNo=:providerNo and x.lastUpdateDate>:updatedAfterThisDateExclusive order by x.lastUpdateDate";

		Query query = entityManager.createQuery(sqlCommand);
		query.setParameter("demographicId", demographicId);
		query.setParameter("providerNo", providerNo);
		query.setParameter("updatedAfterThisDateExclusive", updatedAfterThisDateExclusive);
		setLimit(query, itemsToReturn);
		
		@SuppressWarnings("unchecked")
		List<Prevention> results = query.getResultList();
		return (results);
	}

	public List<Prevention> findNotDeletedByDemographicIdAfterDatetime(Integer demographicId, Date dateTime) {
		Query query = entityManager.createQuery("select x from Prevention x where demographicId=?1 and lastUpdateDate> ?2");
		query.setParameter(1, demographicId);
		query.setParameter(2, dateTime);

		@SuppressWarnings("unchecked")
		List<Prevention> results = query.getResultList();

		return (results);
	}
	
	public List<Integer> findNonDeletedIdsByDemographic(Integer demographicId) {
		Query query = entityManager.createQuery("select x.id from Prevention x where demographicId=?1 and deleted='0'");
		query.setParameter(1, demographicId);
	
		@SuppressWarnings("unchecked")
		List<Integer> results = query.getResultList();

		return (results);
	}
	

	public List<Prevention> findNotDeletedByDemographicId(Integer demographicId) {
		Query query = entityManager.createQuery("select x from "+modelClass.getSimpleName()+" x where demographicId=?1 and deleted=?2");
		query.setParameter(1, demographicId);
		query.setParameter(2, '0');

		@SuppressWarnings("unchecked")
        List<Prevention> results = query.getResultList();

		return (results);
	}

  public List<Prevention> findLatestByTypeNotDeletedByDemographicId(List<Integer> demographicIds) {
    Query query =
        entityManager.createNativeQuery(
            "select p.* from preventions p "
                + "INNER JOIN ("
                + "SELECT newest.prevention_type, MAX(newest.prevention_date) as prevention_date "
                + "FROM preventions newest "
                + "WHERE newest.demographic_no IN (:demographicNumbers) "
                + "AND newest.deleted IS FALSE "
                + "GROUP BY newest.prevention_type) AS newest "
                + "ON p.prevention_type = newest.prevention_type "
                + "AND p.prevention_date = newest.prevention_date "
                + "AND p.demographic_no IN (:demographicNumbers);",
            modelClass);
    query.setParameter("demographicNumbers", demographicIds);

    @SuppressWarnings("unchecked")
    List<Prevention> results = query.getResultList();

    return (results);
  }

  public Map<String, Integer> findCountByTypeNotDeletedByDemographicId(Integer demographicId) {
    Query query =
        entityManager.createNativeQuery(
            "SELECT p.prevention_type, COUNT(p.id) FROM preventions p "
                + "WHERE p.demographic_no = ?1 AND p.deleted = FALSE GROUP BY p.prevention_type;");
    query.setParameter(1, demographicId);

    @SuppressWarnings("unchecked")
		val resultsList = query.getResultList();
		val resultsMap = new HashMap<String, Integer>();
		for (Object o : resultsList) {
			val result = (Object[]) o;
      resultsMap.put((String) result[0], ((BigInteger) result[1]).intValue()); // if a demographic has more
		}
		return resultsMap;
  }

	public List<Prevention> findNotDeletedByDemographicIds(List<Integer> demographicIds) {
		Query query = entityManager.createQuery("select x from "+modelClass.getSimpleName()+" x where demographicId IN (:demogrpahicNos) and deleted=:deleted");
		query.setParameter("demographicNos", demographicIds);
		query.setParameter("deleted", '0');

		@SuppressWarnings("unchecked")
		List<Prevention> results = query.getResultList();

		return (results);
	}

	public List<Prevention> findByTypeAndDate(String preventionType, Date startDate, Date endDate) {
		Query query = entityManager.createQuery("select x from "+modelClass.getSimpleName()+" x where preventionType=?1 and preventionDate>=?2 and preventionDate<=?3 and deleted='0' and (refused='0' or refused='3') order by preventionDate");
		query.setParameter(1, preventionType);
		query.setParameter(2, startDate);
		query.setParameter(3, endDate);

		@SuppressWarnings("unchecked")
        List<Prevention> results = query.getResultList();

		return (results);
	}

  public List<Prevention> findByTypeAndDemoNos(String preventionType, List<Integer> demoNos) {
    Query query = entityManager.createNativeQuery(
        "SELECT id, demographic_no, creation_date, MAX(prevention_date) AS prevention_date, " +
            "provider_no, provider_name, prevention_type, deleted, refused, never, next_date, " +
            "creator, lastUpdateDate, isAvailable, autoSyncDate, lastSyncedDate, " +
            "location, snomedId, remoteSystemId, guid " +
            "FROM preventions x " +
            "WHERE prevention_type = :preventionType " +
            "AND demographic_no IN (:demographicNos) " +
            "AND deleted = '0' " +
            "GROUP BY demographic_no " +
            "ORDER BY prevention_date",
        Prevention.class);
    query.setParameter("preventionType", preventionType);
    query.setParameter("demographicNos", demoNos);

    @SuppressWarnings("unchecked")
    List<Prevention> results = query.getResultList();
    return (results);
  }

	public List<Prevention> findByTypeAndDemoNo(String preventionType, Integer demoNo) {
		Query query = entityManager.createQuery("select x from "+modelClass.getSimpleName()+" x where preventionType=?1 and demographicId=?2 and deleted='0' order by preventionDate");
		query.setParameter(1, preventionType);
		query.setParameter(2, demoNo);

		@SuppressWarnings("unchecked")
		List<Prevention> results = query.getResultList();
		return (results);
	}

	@SuppressWarnings("unchecked")
    public List<Prevention> findActiveByDemoId(Integer demoId) {
		Query query = createQuery("p", "p.demographicId = :demoNo and p.deleted <> '1' ORDER BY p.preventionType, p.preventionDate");
		query.setParameter("demoNo", demoId);
		return query.getResultList();
	}
	
	public List<Prevention> findActiveByDemoIdWithDates(Integer demoId, Date startDate, Date endDate) {
		Query query = entityManager.createQuery("select x from "+modelClass.getSimpleName()+" x where demographicId=?1 and preventionDate>=?2 and preventionDate<=?3 and deleted='0' and refused='0' order by preventionDate DESC");
		query.setParameter(1, demoId);
		query.setParameter(2, startDate);
		query.setParameter(3, endDate);

		@SuppressWarnings("unchecked")
        List<Prevention> results = query.getResultList();

		return (results);
	}

	public List<Prevention> findUniqueByDemographicId(Integer demographicId) {
		Query query = entityManager.createNativeQuery("SELECT p1.* FROM preventions p1 left join preventions as p2 on p1.prevention_type = p2.prevention_type and p1.demographic_no = p2.demographic_no and p2.deleted='0' and (p1.prevention_date < p2.prevention_date OR (p1.prevention_date = p2.prevention_date and p1.id < p2.id)) where p1.demographic_no = :demographicId AND p1.deleted='0' AND p2.id is null ORDER BY p1.prevention_date DESC", Prevention.class);
		query.setParameter("demographicId", demographicId);

		@SuppressWarnings("unchecked")
		List<Prevention> results = query.getResultList();
		
		return (results);
	}
	
	public List<Prevention> findUniqueByDemographicIdAndTypes(
			Integer demographicId, List<String> preventionTypes) {
		Query query = entityManager.createNativeQuery(
				"SELECT p1.* " 
						+ "FROM preventions p1 " 
						+ "left join preventions as p2 " 
						+ "on p1.prevention_type = p2.prevention_type " 
						+ "and p1.demographic_no = p2.demographic_no " 
						+ "and p2.deleted='0' " 
						+ "and (p1.prevention_date < p2.prevention_date " 
						+ "OR (p1.prevention_date = p2.prevention_date " 
						+ "and p1.id < p2.id)) " 
						+ "where p1.demographic_no = :demographicId " 
						+ "AND p1.deleted='0' " 
						+ "AND p2.id is null "
						+ "AND p1.prevention_type IN (:types) "
						+ "ORDER BY p1.prevention_date DESC", 
				Prevention.class);
		query.setParameter("demographicId", demographicId);
		query.setParameter("types", preventionTypes);

		@SuppressWarnings("unchecked")
		List<Prevention> results = query.getResultList();
		
		return (results);
	}

	public Prevention findByGuid(String guid) {
		Query query = entityManager.createNativeQuery("SELECT p1.* "
				+ "FROM preventions p1 "
				+ "WHERE guid=?1",
				Prevention.class);
		query.setParameter(1, guid);

		@SuppressWarnings("unchecked")
		List<Prevention> results = query.getResultList();

		if (results.size() == 0) {
			return null;
		} else {
			return (results.get(0));
		}
	}
	
	@NativeSql("preventions")
	public List<Integer> findNewPreventionsSinceDemoKey(String keyName) {
		
		String sql = "select distinct dr.demographic_no from preventions dr,demographic d,demographicExt e where dr.demographic_no = d.demographic_no and d.demographic_no = e.demographic_no and e.key_val=? and dr.lastUpdateDate > e.value";
		Query query = entityManager.createNativeQuery(sql);
		query.setParameter(1,keyName);
		return query.getResultList();
	}

	public List<Prevention> fetchRemotePreventions(final Integer demographicNumber) {
		if (demographicNumber == null) {
			return new ArrayList<>();
		}
		val demographic = demographicDao.getDemographic(demographicNumber);
		return fetchRemotePreventions(demographic);
	}

	public List<Prevention> fetchRemotePreventions(final Demographic demographic) {
		if (demographic == null
				|| !gwConfigurationService.isLinkFeatureEnabled(FeatureFlagEnum.PREVENTIONS_ENABLED)
		) {
			return new ArrayList<>();
		}

		val preventionList = new ArrayList<Prevention>();

		try {
			val bundle =
					gatewayDao.findAllRemoteByDemographicId(
							Immunization.class,
							demographic,
							new HashMap<>()
					);
			if (bundle != null && bundle.hasEntry()) {
				val immunizationConverter = new ImmunizationConverter();
				for (val entry : bundle.getEntry()) {
					val immunization = (Immunization) entry.getResource();
					val prevention = immunizationConverter.toOscarObject(immunization);
					preventionList.add(prevention);
				}
			}
		} catch (Exception e) {
			System.err.println("Error fetching remote Immunizations: " + e.getMessage());
		}

		return preventionList;
	}

	public Prevention findRemoteById(final String identifier, final Demographic demographic) {
		try {
			val immunization = gatewayDao.findRemoteById(Immunization.class, demographic, identifier);
			val immunizationConverter = new ImmunizationConverter();
			return immunizationConverter.toOscarObject((Immunization) immunization);
		} catch (Exception e) {
			log.error("Failed to fetch prevention from gateway", e);
			return null;
		}
	}

	public void saveToPrimary(final Prevention prevention, final Demographic demographic) {
		if (!isValidSavePreventionToPrimary(demographic))  {
			return;
		}
		try {
			val immunizationConverter = new ImmunizationConverter();
			val immunization = immunizationConverter.toFhirObject(prevention,
					PreventionUtil.getPreventionExts(prevention.getId()));
			gatewayDao.saveRemote(demographic, immunization);
		} catch (Exception e) {
			log.error("Failed to save Prevention via gateway", e);
			ticklerManager.createSendToPrimaryFailedTicklerForPrevention(prevention);
		}
	}

	private boolean isValidSavePreventionToPrimary(final Demographic demographic) {
		return demographic.isLinked()
				&& gwConfigurationService.isLinkFeatureEnabled(FeatureFlagEnum.PREVENTIONS_ENABLED);
	}

}
