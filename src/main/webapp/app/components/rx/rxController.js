rx.requires = rx.requires.concat(['appointmentServices', 'authenticationServices', 'demographicServices',
    'diseaseRegistryServices', 'propertyServices', 'providerServices', 'providerPreferenceServices',
    'specialistServices', 'constantsServices', 'ngAnimate', 'ui.bootstrap', 'ui.router', 'plugin', 'infinite-scroll',
    'indeterminateCheckbox', 'signature-canvas', 'erx-status-badges', 'erx-drug-actions', 'messageServices',
    'quick-modal', 'resourceStorageServices', 'rxUtil', 'dataSharingServices', 'buttonDatePicker', 'well-ai-voice-script', 'wellAiVoiceService'
]);

rx.controller('rxController', function($scope, $state, $filter, $rootElement, $rootScope, $location, $window, $q,
                                       $uibModal, $uibModalStack,  $controller, $interval, rxService, appointmentService,
                                       authenticationService, demographicService, diseaseRegistryService, propertyService, providerService,
                                     preferenceService, specialistService, constantsService, quickModal, resourceStorageService,
                                       rxUtil, dataSharingService, eChartService){
    const PILLWAY_BUTTON_ENABLED = 'pillway.home_delivery_button_enabled';
    const PILLWAY_ID = 'PILLWAY';
    const DRUG_INTERACTION_WARNING_LEVEL = 'rxInteractionWarningLevel';
    var urlParams = $location.search();
    $scope.isLoading = {
        btn: {
            eSign: false,
            save: false
        },
        page: true,
        section: {
            itemList: true
        },
    };

    $scope.selectedJurisdiction = {enabled: false};

    $scope.allergy = {
        id: '',
        demographicNo: '',
        providerNo: '',
        entryDate: new Date(),
        description: '',
        reaction: '',
        archived: false,
        hiclSeqNo: null,
        hicSeqNo: null,
        agcsp: null,
        typeCode: null,
        drugrefId: null,
        startDate: null,
        ageOfOnset: '',
        severityOfReaction: '4',
        onsetOfReaction: '4',
        regionalIdentifier: null,
        lifeStage: null,
        position: 0,
        lastUpdateDate: new Date(),
        lookupError: false
    };

    $scope.allergyFilters = {
        list: 'active',
        orderBy:  ['archived', 'description'],
        reverseOrder: false
    };

    $scope.isCustomDrugSelected = false;

    $scope.demographic = null;
    $scope.appointmentNo = null;

    $scope.rxItemOpen = [];

    $scope.durations = [
        {unit: 'D', description: 'days', durationInDays: 1},
        {unit: 'W', description: 'weeks', durationInDays: 7},
        {unit: 'M', description: 'months', durationInDays: 30},
        {unit: 'Y', description: 'years', durationInDays: 365}
    ];

    $scope.viewPharmacy = false;

    $scope.editAllergy = null;
    $scope.editCategory = null;



    $scope.filters = [
        {key: 'current', description: 'Current Drugs', erxOnly: false},
        {key: 'active', description: 'Active Drugs', erxOnly: false},
        {key: 'expired', description: 'Expired Drugs', erxOnly: false},
        {key: 'longTerm', description: 'Long Term Drugs', erxOnly: false},
        {key: 'all', description: 'All Drugs', erxOnly: false},
        {key: 'pendingCancel', description: 'Pending eRx cancellations', erxOnly: true}
        ];

    $scope.formularyQueryState = {error: 'error', loading: 'loading', none: ''};

    $scope.formularyQueryItemStates = [];

    $scope.frequencies = [
        {unit: 'OD', description: 'once daily', frequencyInDays: 1},
        {unit: '1D', description: 'once a day ', frequencyInDays: 1},
        {unit: 'QAM', description: 'every morning', frequencyInDays: 1},
        {unit: 'QPM', description: 'every evening', frequencyInDays: 1},
        {unit: 'QHS', description: 'every day at bedtime', frequencyInDays: 1},
        {unit: 'HS', description: 'at bedtime', frequencyInDays: 1},
        {unit: 'QAM&PM', description: 'every morning and every evening ', frequencyInDays: 2},
        {unit: 'QAM&HS', description: 'every morning and at bedtime ', frequencyInDays: 2},
        {unit: 'NOON', description: 'at noon ', frequencyInDays: 1},
        {unit: 'QLUNCH', description: 'at lunch', frequencyInDays: 1},
        {unit: 'QDINNER', description: 'at dinner', frequencyInDays: 1},
        {unit: 'BID', description: 'twice daily', frequencyInDays: 2},
        {unit: '2D', description: 'twice a day', frequencyInDays: 2},
        {unit: 'TID', description: '3 times daily', frequencyInDays: 3},
        {unit: '3D', description: '3 times a day', frequencyInDays: 3},
        {unit: 'QID', description: '4 times daily', frequencyInDays: 4},
        {unit: '4D', description: '4 times a day', frequencyInDays: 4},
        {unit: '5D', description: '5 times a day', frequencyInDays: 5},
        {unit: '6D', description: '6 times a day', frequencyInDays: 6},
        {unit: 'QD-BID', description: '1 to 2 times daily ', frequencyInDays: 2},
        {unit: 'BID-TID', description: '2 to 3 times a day', frequencyInDays: 3},
        {unit: 'BID-QID', description: '2 to 4 times a day', frequencyInDays: 4},
        {unit: 'TID-QID', description: '3 to 4 times a day', frequencyInDays: 4},
        {unit: '6D-8D', description: '6 to 8 times a day', frequencyInDays: 8},
        {unit: 'QH', description: 'every hour', frequencyInDays: 24},
        {unit: 'Q1H', description: 'every 1 hour', frequencyInDays: 24},
        {unit: 'Q2H', description: 'every 2 hours', frequencyInDays: 12},
        {unit: 'Q4H', description: 'every 4 hours', frequencyInDays: 6},
        {unit: 'Q6H', description: 'every 6 hours', frequencyInDays: 4},
        {unit: 'Q8H', description: 'every 8 hours', frequencyInDays: 3},
        {unit: 'Q12H', description: 'every 12 hours', frequencyInDays: 2},
        {unit: 'Q4-6H', description: 'every 4 to 6 hours', frequencyInDays: 6},
        {unit: 'Q6-8H', description: 'every 6 to 8 hours', frequencyInDays: 4},
        {unit: 'Q24H', description: 'every 24 hours', frequencyInDays: 1},
        {unit: 'Q72H', description: 'every 72 hours  ', frequencyInDays: 1/3},
        {unit: 'Q5MIN', description: 'every 5 min', frequencyInDays: 288},
        {unit: 'Q2D', description: 'every 2 days', frequencyInDays: 1/2},
        {unit: 'QOTHERD', description: 'every other day', frequencyInDays: 1/2},
        {unit: 'Q2NDD', description: 'every 2nd day', frequencyInDays: 1/2},
        {unit: 'Q3D', description: 'every 3 days ', frequencyInDays: 1/3},
        {unit: 'Q3RDD', description: 'every 3rd day', frequencyInDays: 1/3},
        {unit: 'Q1W', description: 'once a week ', frequencyInDays: 1/7},
        {unit: 'Q1TW ', description: 'one time per week ', frequencyInDays: 1/7},
        {unit: 'QW', description: 'once weekly', frequencyInDays: 1/7},
        {unit: 'Q2W', description: 'once every 2 weeks', frequencyInDays: 1/14},
        {unit: 'Q1M', description: 'once a month', frequencyInDays: 1/30},
        {unit: 'QM', description: 'once monthly', frequencyInDays: 1/30},
        {unit: 'Q2M', description: 'every 2 months', frequencyInDays: 1/60},
        {unit: 'Q3M', description: 'every 3 months', frequencyInDays: 1/90},
        {unit: '2TW', description: 'twice weekly', frequencyInDays: 2/7},
        {unit: '2TPW', description: 'two times per week ', frequencyInDays: 2/7},
        {unit: '3TW', description: 'three times weekly', frequencyInDays: 3/7},
        {unit: '3TPW', description: 'three times per week ', frequencyInDays: 3/7},
        {unit: 'QMWSS', description: 'on Mon, Wed, Sat and Sun each week', frequencyInDays: 4/7},
        {unit: 'QTTF', description: 'on Tues, Thurs and Fri each week', frequencyInDays: 3/7},
        {unit: 'Q1Week', description: 'once weekly', frequencyInDays: 1/7},
        {unit: 'Q2Week', description: 'once every 2 weeks', frequencyInDays: 1/14},
        {unit: 'Q1Month', description: 'once every month', frequencyInDays: 1/30},
        {unit: 'Q2Month', description: 'once every 2 months', frequencyInDays: 1/60},
        {unit: 'Q3Month', description: 'once every 3 months', frequencyInDays: 1/90}
    ];

    $scope.lifeStage = [
        {key: '', description: 'Not Set', ageRange: ''},
        {key: 'N', description: 'Newborn', ageRange: 'Birth - 28 days'},
        {key: 'I', description: 'Infant', ageRange: '29 days - under 2 years'},
        {key: 'C', description: 'Child', ageRange: '2 - 15 years'},
        {key: 'T', description: 'Adolescent', ageRange: '16 - 17 years'},
        {key: 'A', description: 'Adult', ageRange: '18+ years'}
    ];

    $scope.onsetOfReaction = [
        {key: '1', description: 'Immediate'},
        {key: '2', description: 'Gradual'},
        {key: '3', description: 'Slow'},
        {key: '4', description: 'Unknown'}
    ];

    $scope.processing = {
        show: false,
        message: ''
    };

    $scope.rx = {
        scriptNo: null,
        providerNo: null,
        demographicNo: null,
        datePrescribed: null,
        datesReprinted: null,
        textView: '',
        comments: null,
        lastUpdateDate: null,
        deliveryMethod: null,
        doNoAutofill: false,
        items: [],
        siteId: null
    };

    $scope.medicationListAllSelect = false;
    $scope.medicationListWAttrs = [];

    $scope.typeAheadRx = {
        input: "",
        options: []
    };
    $scope.isRxSearchActive = false;
    $scope.lastRxSearch = '';

    $scope.severityOfReaction = [
        {key: '1', description: 'Mild', styleClass: 'is-warning', icon: 'fa-exclamation-circle'},
        {key: '2', description: 'Moderate', styleClass: 'is-warning-orange', icon: 'fa-exclamation-triangle'},
        {key: '3', description: 'Severe', styleClass: 'is-danger', icon: 'fa-times-circle'},
        {key: '4', description: 'Unknown', styleClass: 'is-info', icon: 'fa-question-circle'}
    ];

    var defaultQuantity = 0; //from propertyService.readProperty("rx_default_quantity", newPrescription.getProviderNo())
    var defaultInstruction = ''; //from "rx.default_instruction"

    $scope.demographicAllergies = [];
    $scope.examples = []

    $scope.drug = {
        id: null,
        providerNo: $scope.rx.providerNo,
        demographicNo: $scope.rx.demographicNo,
        demographic: $scope.rx.demographic,
        rxDate: new Date(),
        endDate: null,
        writtenDate: new Date(),
        name: null,
        brandName: null,
        gcnSeqNo: 0,
        customName: null,
        takeMin: 0,
        takeMax: 0,
        freqCode: null,
        duration: null,
        durationUnit: null,
        quantity: defaultQuantity,
        dispensingUnits: null,
        repeat: 0,
        lastRefillDate: null,
        noSubs: false,
        prn: false,
        special: defaultInstruction,
        parseInstructions: true,
        instructions: '',
        archived: false,
        archivedDate: null,
        genericName: null,
        atc: null,
        scriptNo: $scope.rx.scriptNo,
        regionalIdentifier: null,
        unit: null,
        method: '',
        route: null,
        drugForm: null,
        createDate: new Date(),
        dosage: null,
        customInstructions: false,
        unitName: null,
        longTerm: false,
        pastMed: false,
        patientCompliance: null,
        outsideProviderName: null,
        outsideProviderOhip: null,
        outsideProviderCheckbox: false,
        hideFromDrugProfile: null,
        customNote: false,
        isCustom: false,
        pickupDateTime: null,
        eTreatmentType: null,
        rxStatus: null,
        hideFromCpp: false,
        refillDuration: 0,
        refillQuantity: 0,
        dispenseInterval: "0",
        position: 0,
        startDateUnknown: false,
        comment: null,
        lastUpdateDate: null,
        dispenseInternal: false,
        controlledSubstanceInfo: null,
        prescriptionIdentifier: null,
        priorRxIdentifier: null,
        protocolId: null,
        formularyResults: [],
        interactionWarnings: [],
        pharmacyInstructions: defaultInstruction,
        hasTask: false,
        allergyWarning: ''
    };

    $scope.interactionWarnings = [];

    $scope.allergyWarnings = [];

    $scope.pharmacyItem = {};

    $scope.pharmacies = [];

    $scope.prefPharmacies = [];
    $scope.prefPharmaciesOptions = [];

    $scope.allPharmacies = [];

    $scope.medicationList = [];

    $scope.pharmacySearch = "";

    $scope.providerRegistrySearch = "";

    $scope.registrySearchResult = {
        totalPossibleResults: 0,
        pharmacies: []
    };

    $scope.registryResults = false;

    $scope.validPrescription = true;

    $scope.errorMessage = "";
    $scope.errorMessageContext = {};

    $scope.selectedFilter = {key: 'current', description: 'Current Drugs'};

    $scope.controlledSubstanceInfo = {};
    $scope.controlledSubstanceDemographicIdTypes = [];

    $scope.currentPharmacy = {
        pharmacyInfo : {
            id: '',
            name: '',
            address: '',
            city: '',
            province: '',
            postalCode: '',
            phone1: '',
            phone2: '',
            fax: '',
            email: '',
            serviceLocationIdentifier: '',
            notes: '',
            addDate: new Date(),
            status: '1'
        },
        preferred: false
    };

    $scope.datePicker = {
        labDateOpened: false
    };

    $scope.eRxSettings =  {
        enabled: false,
        configured: false,
        service: 'Electronic Prescription',
        clinic: {
            configured: false,
            applicationId: null,
            id: null
        },
        provider: {
            configured: false,
            enabled: false,
            id: null
        },
        vendor: {
            name: null,
            software: null,
            version: null
        },
        webservice: {
            host: null,
            path: null
        },
        formulary: {
            url: null
        }
    };

    $scope.ePrescribe = false;

    $scope.eRxFormularyJurisdictions = [];

    $scope.lab = {
        dateFilter:new Date(new Date().setHours(23,59,59,0)),
        index: 0,
        path: '',
        show: true
    };

    $scope.disableWarningLabel = false;
    $scope.lastUsedReason = '';

    $scope.rxOverride = false;
    $scope.rxOverrideFieldName = null;

    propertyService.findByNameAndRequestProvider(PILLWAY_BUTTON_ENABLED).then(function (result) {
        $scope.pillwayButtonEnabled = !result || result.length === 0 || result[0].value === "true";
    });

    $scope.favouriteKeyword = '';

    $scope.mainWrittenDate = new Date();

    $scope.warningLabelToggle = function() {
        $scope.disableWarningLabel = !$scope.disableWarningLabel;
        localStorage.setItem('disableWarningLabel', JSON.stringify($scope.disableWarningLabel));
    }

    $scope.updateLastUsedReason = function(reason) {
        $scope.lastUsedReason = reason;
    }

    $scope.recentMedicationDates = {};

    $scope.addDrug = function (drug) {
        if (!drug) {
            resetDrug();
        } else {
            let duplicateDrug = $scope.rx.items.find(existingDrug => existingDrug.name === drug.name && existingDrug.dosage === drug.dosage);
            if (duplicateDrug) {
                return;
            }
            $scope.rx.items.sort((a, b) => {
                return a.lastUpdateDate - b.lastUpdateDate
            });
            let itemIndex = $scope.rx.items.push(drug) - 1;
            if (!drug.isCustom) {
                queryFormulary(itemIndex);
                queryDrugInteractions();
                queryPatientAllergies(itemIndex);
            }

            if (drug.regionalIdentifier) {
                resourceStorageService.limitedUseLookup(drug.regionalIdentifier).then(function(data) {
                    drug.limitedUseCodeList = data.data;
                    if (drug.limitedUseCodeList.length > 0) {
                        if ($scope.rxProperties.rx_expand_lu_code_descriptions === 'true') {
                            $scope.toggleAllLimitedUseCodeDescriptions(drug);
                        }
                    }
                });
            }
            $scope.rxItemOpen.push(itemIndex);
        }

        resetDrugSearch();
    };

    $scope.toggleLimitedUseCodeDescription = function (code, drug) {
        code.showDescription = !code.showDescription;

        let allSame = true;

        for (let i = 0; i < drug.limitedUseCodeList.length; i++) {
            if (drug.limitedUseCodeList[i].type === null && !drug.limitedUseCodeList[i].showDescription === code.showDescription) {
                allSame = false;
            }
        }

        if (allSame) {
            drug.showAllLimitedUseCodeDescriptions = code.showDescription;
        }
    };

    $scope.toggleAllLimitedUseCodeDescriptions = function (drug) {
        for (let i = 0; i < drug.limitedUseCodeList.length; i++) {
            drug.limitedUseCodeList[i].showDescription = !drug.showAllLimitedUseCodeDescriptions;
        }
        drug.showAllLimitedUseCodeDescriptions = !drug.showAllLimitedUseCodeDescriptions;
    };

    function updateInstructions(instructions, codeId) {
        if (!instructions) {
            return 'LU Code: ' + codeId;
        }
        const regex = /LU Code: \d+/;
        return instructions.match(regex)
            ? instructions.replace(regex, 'LU Code: ' + codeId)
            : instructions + ' LU Code: ' + codeId;
    }

    function addLuCodeToDosageInstructions(drug, codeId) {
        drug.pharmacyInstructions =
            updateInstructions(drug.pharmacyInstructions, codeId);
        drug.special = updateInstructions(drug.special, codeId);
    }

    $scope.addLuCodeToDosageInstructions = addLuCodeToDosageInstructions;

    $scope.openERxSettingsModal = function () {
        let modalInstance = $uibModal.open({
            templateUrl: 'eRxSettingsModal.html',
            controller: 'eRxSettingsModalController',
            windowClass: 'erx-config-modal',
            resolve: {
                eRxSettings: function () {
                    return $scope.eRxSettings;
                },
                eRxFormularyJurisdictions: function () {
                    return $scope.eRxFormularyJurisdictions;
                },
                loggedInProvider: function () {
                    return $scope.loggedInProvider;
                },
                updateFormularyConfiguration: function () {
                    return $scope.updateFormularyConfiguration;
                },
                toggleERxEnabled: function () {
                    return $scope.toggleERxEnabled;
                }
            }
        });
    };


    var queryFormulary = function(itemIndex) {
        if ($scope.selectedJurisdiction.enabled && $scope.eRxSettings.enabled && $scope.eRxSettings.provider.enabled) {
            var drug = $scope.rx.items[itemIndex];
            drug.isCompoundDrug = drug.genericName.split(" / ").length > 1;
            if (!drug.isCompoundDrug) {
                $scope.setFormularyQueryItemState(itemIndex, $scope.formularyQueryState.loading);
                if (drug.regionalIdentifier != null) {
                    var queryParameters = {
                        demographicNo: $scope.demographic.demographicNumber,
                        jurisdiction: $scope.eRxSettings.formulary.jurisdiction,
                        productCode: drug.regionalIdentifier,
                        codeIsDin: drug.regionalIdType === null ? null : drug.regionalIdType === "http://hl7.org/fhir/NamingSystem/ca-hc-din"
                    };
                    rxService.queryFormulary(queryParameters, $scope.siteId).then(function (result) {
                        $scope.rx.items[itemIndex].formularyResults = result;
                        $scope.setFormularyQueryItemState(itemIndex);
                    }).catch(function(errorData) {
                        // add error
                        $scope.setFormularyQueryItemState(itemIndex, $scope.formularyQueryState.error);
                        console.log(errorData);
                    });
                } else {
                    $scope.setFormularyQueryItemState(itemIndex);
                    $scope.rx.items[itemIndex].formularyResults = [
                        {
                            coverage: {
                                code: '22731000087103',
                                shortName: 'CD',
                                longName: 'Coverage cannot be determined'
                            },
                        }
                    ]
                }
            }
        }
    };

    $scope.openFormularyResultModal = function (drug) {
        var modalInstance = $uibModal.open({
            templateUrl: "formularyResultModal.html",
            controller: 'formularyResultModalController',
            windowClass: 'formulary-result-modal',
            resolve: {
                formularyResults: function () {
                    return drug.formularyResults;
                }
            }
        });
    };

    let queryDrugInteractions = function() {
        for (let drugIndex in $scope.rx.items) {
            $scope.rx.items[drugIndex].interactionWarnings = [];
        }
        rxService.getDrugInteractionWarnings($scope.demographic.demographicNumber, $scope.rx.items, $scope.medicationList)
            .then(function (interactionResults) {
                // match warnings to drugs to be prescribed
                for (let interactionIndex in interactionResults) {
                    let interaction = interactionResults[interactionIndex];
                    for (let drugIndex in $scope.rx.items) {
                        let drugName = $scope.rx.items[drugIndex].name;
                        if (drugName.toLowerCase() === interaction.affectedDrugName.toLowerCase()
                            || drugName.toLowerCase() === interaction.affectingDrugName.toLowerCase()) {
                            $scope.rx.items[drugIndex].interactionWarnings.push(interaction);
                        }
                        $scope.rx.items[drugIndex].interactionWarnings =
                            removeDuplicateObjectsByKey($scope.rx.items[drugIndex].interactionWarnings, 'screenMessage');
                    }
                }
        });
    };

    let removeDuplicateObjectsByKey = function(array, key) {
        return [...new Map(array.map(item => [item[key], item])).values()]
    }

    let queryPatientAllergies = function(newDrugIndex) {
        $scope.rx.items[newDrugIndex].warningMessages = [];
        rxService.getPatientAllergyWarnings(
            $scope.demographic.demographicNumber,
            $scope.rx.items[newDrugIndex],
            $scope.demographicAllergies
        ).then(function success(warningResults) {
            $scope.rx.items[newDrugIndex].allergyWarnings = warningResults;
        }, function error(e){
            console.log(e);
            $scope.rx.items[newDrugIndex].warningMessages.push("Could not load allergy data");
        });
    };
    $scope.clearRx = function() {
        $scope.rx = {
            scriptNo: '',
            providerNo: '',
            demographicNo: '',
            datePrescribed: null,
            datesReprinted: null,
            textView: '',
            comments: null,
            lastUpdateDate: null,
            deliveryMethod: null,
            doNotAutofill: false,
            items: []
        };
    };

    $scope.getDemographicItems = function() {
        rxService.getRxItemsForDemographic($scope.demographic.demographicNumber).then(function(results) {
            $scope.medicationList = results ? results : [];
            $scope.recentMedicationDates =
                getRecentlyUpdatedMedication($scope.medicationList);

            if ($scope.indivicareLinkEnabled
                && $scope.demographic.cachedLinkDemographicState === 'LINKED') {
                $scope.getRemoteDrugs($scope.demographic.demographicNumber, true);
            } else {
                cloneMedicationListWithAttributes();
            }
        }).catch(function(errorData){
            quickModal.popUp("Error loading demographic prescriptions");
            console.log(errorData);
        }).finally(function () {
            $scope.isLoading.section.itemList = false;
        });
        rxService.getDrugInteractionWarnings($scope.demographic.demographicNumber, null).then(function (interactionResults) {
            $scope.interactionWarnings = interactionResults;
        }).catch(function(errorData) {
            console.log(errorData);
        });
        rxService.getPatientAllergyWarnings(
            $scope.demographic.demographicNumber,
            null,
            $scope.demographicAllergies
        ).then(function (allergyWarnings) {
           $scope.allergyWarnings = allergyWarnings;
        }).catch(function (errorData){
            console.log(errorData);
        });
        document.getElementById("currentDrugs").scroll(0,0);
    };

    $scope.getFormularyCoverageClass = function(coverageCode) {
        if (coverageCode == '22701000087106') {
            return 'is-warning';
        } else if (coverageCode == '22731000087103') {
            return 'is-light';
        } else if (coverageCode == '22711000087108') {
            return 'is-primary';
        } else if (coverageCode == '22721000087100') {
            return 'is-danger';
        }
    };

    $scope.getFormularyQueryItemState = function(itemIndex) {
        let fqItemStateObj = $filter('filter')($scope.formularyQueryItemStates, {itemIndex: itemIndex}, true)[0];
        return fqItemStateObj != null ? fqItemStateObj.state : $scope.formularyQueryState.none;
    }


    $scope.getDrugWarningClass = function(severity) {
        if (severity == '3') {
            return 'is-danger';
        } else if (severity == '1') {
            return 'is-warning';
        } else {
            return 'is-warning-orange';
        }
    };
    $scope.getSeverityOfReactionDescription = function(severity) {
        if (severity == '3') {
            return 'Severe';
        }
        else if (severity == '2') {
            return 'Moderate';
        }
        else if (severity == '1') {
            return 'Mild';
        } else {
            return 'Unknown'
        }

    };

  propertyService.findByNameAndRequestProvider(DRUG_INTERACTION_WARNING_LEVEL).then(
    function (result) {
      $scope.providerDrugInteractionLevel = parseInt(
          !result || result.length === 0 ? '0' : result[0].value
      );
    });

  $scope.showDrugInteraction = function (severity) {
    return parseInt(severity) >= $scope.providerDrugInteractionLevel;
  };

    $scope.drugToFavourite = function (drug) {
        if (drug) {
            var favourite = {
                id: '',
                providerNo: null,
                name: drug.name,
                brandName: drug.brandName,
                gcnSeqNo: drug.gcnSeqNo,
                customName: drug.customName,
                takeMin: drug.takeMin,
                takeMax: drug.takeMax,
                freqCode: drug.freqCode,
                duration: drug.duration,
                durationUnit: drug.durationUnit,
                quantity: drug.quantity,
                dispensingUnits: drug.quantityUnit,
                repeat: drug.repeat,
                longTerm: drug.longTerm,
                nosubs: drug.noSubs,
                prn: drug.prn,
                special: rxUtil.trimSpecial(drug),
                parseInstructions: drug.parseInstructions,
                genericName: drug.genericName,
                atc: drug.atc,
                regionalIdentifier: drug.regionalIdentifier,
                unit: drug.unit,
                method: drug.method,
                route: drug.route,
                drugForm: drug.drugForm,
                dosage: drug.dosage,
                customInstructions: drug.customInstructions,
                unitName: drug.unitName,
                dispenseInternal: drug.dispenseInternal,
                categoryId: 0,
                deleted: false,
                site: drug.site
            };
            $scope.saveFavourite(favourite);
        }
    };

    $scope.openManagePharmaciesModal = function() {
        var modalInstance = $uibModal.open({
            templateUrl: "../pharmacy/managePharmaciesModal.html",
            controller: 'managePharmaciesController',
            size: 'xl',
            windowClass: 'manage-pharmacies-modal',
            resolve: {
                demographicNo: function () {
                    return $scope.demographic.demographicNumber;
                },
                siteId: function () {
                    return getManagePharmaciesSiteId();
                },
                isPrescribeItClinic: function () {
                    return isPrescribeItClinic();
                }
            }
        });
        modalInstance.result.then(function(preferredPharmacies){
            let selectedPharmacy = angular.copy($scope.selectedPharmacy);
            $scope.prefPharmacies = preferredPharmacies;
            if ($scope.prefPharmacies.length > 0) {
                let matched = $scope.prefPharmacies.filter(pharmacy => pharmacy.pharmacyInfo.id === selectedPharmacy.id);
                if (matched.length > 0) {
                    selectedPharmacy = matched[0].pharmacyInfo;
                } else {
                    selectedPharmacy = $scope.prefPharmacies[0].pharmacyInfo;
                }
            } else if ($scope.prefPharmacies.length === 0) {
                selectedPharmacy = null;
            }
            $scope.selectedPharmacy = selectedPharmacy;

            $scope.prefPharmaciesOptions = [{option: 'None', value: '', obj: null}];
            angular.forEach($scope.prefPharmacies, function (res) {
                $scope.prefPharmaciesOptions.push({option: res.pharmacyInfo.displayName ? res.pharmacyInfo.displayName : res.pharmacyInfo.name, value: res.pharmacyInfo.id, obj: res.pharmacyInfo});
            });
        });
        $rootScope.modalLoaded = true;
        $controller('managePharmaciesModalController', {$scope: $scope, $uibModalInstance: modalInstance})
    }

    function getManagePharmaciesSiteId() {
        // On multisite, the selected location on the Rx page contains the siteId
        // On single site, 0 is the default site id
        return $scope.isMultisite ? $scope.siteId : 0;
    }

    function isPrescribeItClinic() {
        if ($scope.isMultisite) {
            // input site is within the list of provider sites
            let site = $filter('filter')($scope.providerSites, {siteId: $scope.siteId})[0];
            // site has a cprId
            return !!(site && site.cprId);
        }
        // on single site, check if the site has a cprId
        return $scope.rxProperties.erx_clinic_id;
    }

    $scope.favouriteToDrug = function (favourite) {
        $scope.drug.name = favourite.name;
        var drug = {
            id: null,
            providerNo: $scope.rx.providerNo,
            demographicNo: $scope.rx.demographicNo,
            demographic: $scope.rx.demographic,
            rxDate: new Date(),
            endDate: null,
            writtenDate: new Date(),
            name: favourite.isCustom ? favourite.customName : favourite.brandName,
            brandName: favourite.brandName,
            gcnSeqNo: favourite.gcnSeqNo,
            customName: favourite.customName,
            takeMin: favourite.takeMin,
            takeMax: favourite.takeMax,
            freqCode: favourite.freqCode,
            duration: favourite.duration,
            durationUnit: favourite.durationUnit,
            quantity: favourite.quantity,
            dispensingUnits: favourite.dispensingUnits,
            repeat: favourite.repeat,
            lastRefillDate: null,
            nosubs: favourite.nosubs,
            prn: favourite.prn,
            special: rxUtil.trimSpecial(favourite),
            specialInstruction: '',
            parseInstructions: favourite.parseInstructions,
            archived: false,
            archivedDate: null,
            genericName: favourite.genericName,
            atc: favourite.atc,
            scriptNo: $scope.rx.scriptNo,
            regionalIdentifier: favourite.regionalIdentifier,
            unit: favourite.unit,
            quantityUnit: favourite.dispensingUnits,
            method: favourite.method,
            route: favourite.route,
            drugForm: favourite.drugForm,
            createDate: new Date(),
            dosage: favourite.dosage,
            customInstructions: favourite.customInstructions,
            unitName: favourite.unitName,
            longTerm: favourite.longTerm,
            site: favourite.site,
            pastMed: false,
            patientCompliance: null,
            outsideProviderName: null,
            outsideProviderOhip: null,
            outsideProviderCheckbox: false,
            hideFromDrugProfile: null,
            customNote: false,
            isCustom: favourite.isCustom,
            pickupDateTime: null,
            eTreatmentType: null,
            rxStatus: null,
            hideFromCpp: false,
            refillDuration: 0,
            refillQuantity: 0,
            dispenseInterval: "0",
            position: 0,
            startDateUnknown: false,
            comment: null,
            lastUpdateDate: null,
            dispenseInternal: favourite.dispenseInternal,
            controlledSubstanceInfo: null,
            prescriptionIdentifier: null,
            priorRxIdentifier: null,
            protocolId: null,
            pharmacyInstructions: null
        };
        var lastIndex = 0;
        if ($scope.rx.items.length > 1){
            lastIndex = $scope.rx.items.length - 1;
        }

        var lastDrugInRx = $scope.rx.items[lastIndex];
        if (lastDrugInRx && !lastDrugInRx.name) {
            $scope.rx.items.splice(lastIndex, 1);
            $scope.addDrug(drug);
        } else {
            $scope.addDrug(drug);
        }

    };

    $scope.rePrescribe = function (oldDrug) {
        event.stopPropagation();
        if ($scope.indivicareLinkEnabled && oldDrug.remoteDrug) {
            rxService.rePrescribeRemoteDrug(oldDrug).then(function (newDrug) {
                $scope.rePrescribeDrug(newDrug);
            }).catch(function () {
                alertify.error('Failed to rePrescribe remote drug.');
            });
        } else {
            rxService.getRePrescribeDrug(oldDrug.id).then(function (newDrug) {
                $scope.rePrescribeDrug(newDrug);
            });
        }
        $scope.disableRxOverride();
    };

    $scope.rePrescribeDrug = function (newDrug) {
        if (newDrug.discontinued && newDrug.archivedReason.toLowerCase() !== 'represcribed') {
            newDrug = checkDiscontinued(newDrug);
        }

        if (newDrug != null) {
            let duplicateDrug = $scope.rx.items.find(drug => drug.name === newDrug.name && drug.dosage === newDrug.dosage);
            if (duplicateDrug) {
                return;
            }
            if (newDrug.isCustom === true) {
                newDrug.name = newDrug.customName;
            } else {
                newDrug.name = newDrug.brandName;
            }
            newDrug.special = rxUtil.trimSpecial(newDrug);
            var lastIndex = 0;
            if ($scope.rx.items.length > 1) {
                lastIndex = $scope.rx.items.length - 1;
            }
            var lastDrugInRx = $scope.rx.items[lastIndex];
            if (lastDrugInRx && !lastDrugInRx.name) {
                $scope.rx.items.splice(lastIndex, 1);
                $scope.addDrug(newDrug);
            } else {
                $scope.addDrug(newDrug);
            }
        }
    }

    $scope.rePrescribeSelected = function () {
        // filter selected displayed items
        let filteredMedWAttrList = $filter('filter')($scope.medicationListWAttrs, $scope.filterItems);
        filteredMedWAttrList = $filter('filter')(filteredMedWAttrList, {selected: true});
        filteredMedWAttrList.forEach(function(medWAttr){
            try {
                // get object without additional attributes
                let medication = $filter('filter')($scope.medicationList, {id: medWAttr.id}, true)[0];
                if(medication != null) {
                    $scope.rePrescribe(medication);
                }
            } catch (e) {
                // no matching medication
            }
            medWAttr.selected = false;
        });
    };

    $scope.pushToPortal = function () {
        // filter selected displayed items
    	let medications = [];
        let filteredMedWAttrList = $filter('filter')($scope.medicationListWAttrs, $scope.filterItems);
        filteredMedWAttrList = $filter('filter')(filteredMedWAttrList, {selected: true});
        filteredMedWAttrList.forEach(function(medWAttr){
            try {
                // get object without additional attributes
                let medication = $filter('filter')($scope.medicationList, {id: medWAttr.id}, true)[0];
                if(medication != null) {
//                    $scope.rePrescribe(medication);
                	medications.push(medication);
                }
            } catch (e) {
                // no matching medication
            }
            medWAttr.selected = false;
        });

        dataSharingService.pushMedicationsToPortal(
            $scope.demographic.demographicNumber,
            medications
        ).then(function (results) {
        	$scope.init();
        });

    };

    $scope.reRxAllLongTerm = function(){
        $scope.medicationList.forEach(function(medication) {
            if (shouldReRxLongTermMedication(medication)) {
                $scope.rePrescribe(medication);
            }
        });
    };

    const shouldReRxLongTermMedication = function(medication) {
        return medication.longTerm && !medication.archived
            && medication.lastUpdateDate ===
            $scope.recentMedicationDates[medication.name];
    }

    const getRecentlyUpdatedMedication = function(drugList) {
        const updateDates = {};
        drugList.forEach(drug => {
            if (!(drug.name in updateDates) || drug.lastUpdateDate > updateDates[drug.name]) {
                updateDates[drug.name] = drug.lastUpdateDate;
            }
        });
        return updateDates;
    }

    let cloneMedicationListWithAttributes = function () {
        $scope.medicationListWAttrs = angular.copy($scope.medicationList);
        $scope.medicationListWAttrs.forEach(function(item) {
            item.selected = false;
        });
    }

    var checkDiscontinued = function(drug) {
        quickModal.confirm('This drug was discontinued on {{ ctrl.archivedDate }} because of {{ ctrl.archivedReason }} are you sure you want to continue it?',
            function (e) {
                if (e) {
                    drug.archived = false;
                    drug.discontinued = false;
                    drug.archivedReason = null;
                    drug.archivedDate = null;
                } else {
                    drug = null;
                }
            },
            {   archivedDate: drug.archivedDate,
                archivedReason: drug.archivedReason
            });
        return drug;
    };

    $scope.checkERxConfig = function() {
        var error = "";

        if ($scope.eRxSettings.enabled) {
            if ($scope.eRxSettings.vendor.name && $scope.eRxSettings.vendor.software && $scope.eRxSettings.vendor.version) {
                $scope.eRxSettings.configured = true;

                if ($scope.eRxSettings.clinic.applicationId && $scope.eRxSettings.clinic.id) {
                    $scope.eRxSettings.clinic.configured = true;
                }

                if ($scope.eRxSettings.provider.enabled && $scope.eRxSettings.provider.id) {
                    $scope.eRxSettings.provider.configured = true;
                } else {
                    $scope.eRxSettings.provider.configured = false;
                }

                $scope.ePrescribe = $scope.eRxSettings.clinic.configured && $scope.eRxSettings.provider.configured;
            }
        }
        return error;
    };

    $scope.updateFormularyConfiguration = function() {
        var promises = [];
        promises.push(rxService.setGlobalRxProperty('erx_formulary_url', $scope.eRxSettings.formulary.url));
        promises.push(rxService.setGlobalRxProperty('erx_formulary_jurisdiction', $scope.eRxSettings.formulary.jurisdiction));
        $q.all(promises).then(function () {
            alertify.success("Updated provincial formulary service configuration");
            updateSelectedJurisdiction();
        })
    };

    $scope.toggleERxEnabled = function () {
        if ($scope.eRxSettings.provider.id != null) {
            rxService.setGlobalRxProperty('erx_enabled',
                $scope.eRxSettings.enabled).then(function () {
                alertify.success(
                    'PrescribeIT ' + ($scope.eRxSettings.enabled ? 'enabled'
                        : 'disabled'))
            }).catch(function () {
                alertify.error(
                    'Error ' + ($scope.eRxSettings.enabled ? 'enabling'
                        : 'disabling') + ' PrescribeIT');
                $scope.eRxSettings.enabled = !$scope.eRxSettings.enabled;
            });
        } else {
            alertify.error(`Error enabling PrescribeIT, has it been set 
            up? Please visit apps.health for more info or call Support if you 
            believe this is an error`);
            $scope.eRxSettings.enabled = !$scope.eRxSettings.enabled;
        }
    };

    $scope.filterFavourites = function(favourite) {
        var keyword = $scope.favouriteKeyword ? new RegExp($scope.favouriteKeyword, "i") : null;
        return ((keyword && favourite.name.search(keyword) !== -1) || !keyword);
    };

    $scope.filterAllergies = function(allergy) {
        var filter = $scope.allergyFilters.list;

        if (filter === 'active' && !allergy.archived) {
            return true;
        } else if (filter === 'inactive' && allergy.archived) {
            return true;
        } else if (filter === 'all') {
            return true;
        }

        return false;
    };

    $scope.filterItems = function(item) {
        var returnItem = false;
        var filter = $scope.selectedFilter.key;
        if (filter === 'current' || filter === 'expired' || filter === 'active') {
            var today = new Date();
            var endDate = new Date(item.endDate);

            if (filter === 'expired' && !item.longTerm && endDate.getTime() < today.getTime() && !item.archived) {
                returnItem = true;
            } else if (filter === 'current' && !item.archived) {
                returnItem = true;
            } else if (filter === 'active') {
                returnItem = (endDate.getTime() >= today.getTime() || item.longTerm) && !item.archived;
            }
        } else if (filter === 'pendingCancel') {
            return (item.cancelStatus != null && item.cancelStatus.status == 'PENDING');
        } else if (filter === 'active') {
            returnItem = item.longTerm || !item.archived;
        } else if (filter === 'longTerm') {
            returnItem = item.longTerm && !item.archived;
        } else if (!(item.archived && item.archivedReason == 'SENDING_ERX')) {
            returnItem = true;
        }
        return returnItem;

    };

    $scope.getClasses = function(item) {
        var classes = "";
        if (item) {
            let isCurrent = $scope.isCurrent(item);

            if ($scope.isExpireInReference(item)) {
                classes += "expireInReference ";
            }

            if (isCurrent && item.daysToExpiry <= 30 && !item.longTerm) {
                classes += "month-to-expire ";
            }

            if (item.archived && !item.discontinued) {
                classes += "archived ";
            }

            if (!item.longTerm && !isCurrent) {
                classes += "expired ";
            }

            if (item.longTerm){
                classes += "longTerm ";
            }

            if (item.discontinued){
                classes += "discontinued ";
            }

            if (classes === '' || item.longTerm) {
                classes += 'current ';
            }
        }

        return classes;
    };

    $scope.getReactionTypeDisplay = function(reactionType) {
        if (reactionType === 'AR') {
            return 'Adverse Reaction';
        } else if (reactionType === 'AL') {
            return 'Allergy';
        }
        return '';
    }

    $scope.getLifeStage = function(lifeStageKey) {
        if (!lifeStageKey) {
            lifeStageKey = "";
        }
        return $filter('filter')($scope.lifeStage, {key: lifeStageKey})[0] ? $filter('filter')($scope.lifeStage, {key: lifeStageKey}, true)[0] : $filter('filter')($scope.lifeStage, {key: ""})[0];
    };

    $scope.setLifeStage = function(lifeStageKey) {
        if (!lifeStageKey) {
            lifeStageKey = "";
        }
        $scope.editAllergy.lifeStage = lifeStageKey;
    };

    $scope.getMedWAttr = function(itemId) {
        return $filter('filter')($scope.medicationListWAttrs, {id: itemId}, true)[0];
    }

    $scope.excludeFromRowClick = function() {
        event.stopPropagation();
    }

    $scope.getMostRecentLab = function (direction) {
        if ($scope.demographic.labs && $scope.demographic.labs.length > 0) {
            if (direction === 'next' && ($scope.lab.index < ($scope.demographic.labs.length - 1))) {
                $scope.lab.index += 1;
            } else if (direction === 'previous' && $scope.lab.index > 0) {
                $scope.lab.index -= 1;
            }
            $scope.lab.path = `/${__env.oscarContext}/lab/CA/ALL/labDisplay.jsp?demographicId=` + $scope.demographic.demographicNumber + "&segmentID=" + $scope.demographic.labs[$scope.lab.index].segmentId;
        }
    };

    window.resizeIFrame = function (maxHeight) {
        let iFrame = event.currentTarget;
        let height = iFrame.contentWindow.document.body.scrollHeight;
        iFrame.style.height = (height > maxHeight ? maxHeight : height) + "px";
    };

    $scope.hasMoreLabs = function () {
        return $scope.demographic != null && $scope.demographic.labs && $scope.demographic.labs.length > 0 && ($scope.lab.index < $scope.demographic.labs.length - 1);
    };

    $scope.getOnsetOfReaction = function(reactionKey) {
        if (!reactionKey) {
            reactionKey = "4";
        }
        return $filter('filter')($scope.onsetOfReaction, {key: reactionKey})[0] ? $filter('filter')($scope.onsetOfReaction, {key: reactionKey}, true)[0] : $filter('filter')($scope.onsetOfReaction, {key: "4"}, true)[0];
    };

    $scope.setOnsetOfReaction = function(reactionKey) {
        if (!reactionKey) {
            reactionKey = "4";
        }

        $scope.editAllergy.onsetOfReaction = reactionKey;
    };

    $scope.getSeverityOfReaction = function(reactionKey) {
        if (!reactionKey) {
            reactionKey = "4";
        }
        return $filter('filter')($scope.severityOfReaction, {key: reactionKey})[0] ? $filter('filter')($scope.severityOfReaction, {key: reactionKey}, true)[0] : $filter('filter')($scope.severityOfReaction, {key: "4"}, true)[0];
    };

    $scope.setSeverityOfReaction = function(reactionKey) {
        if (!reactionKey) {
            reactionKey = "4";
        }
        $scope.editAllergy.severityOfReaction = reactionKey;
    };

    $scope.pillwayPharmacy = null;

    $scope.isMultisite = false;
    $scope.siteId = null;
    $scope.allSites = [];
    $scope.providerSites = [];
    $scope.tokenSiteId = null;

    $scope.setSelectedSite = function(siteId) {
        $scope.siteId = siteId;
        updateERxProperties();
    };

    $scope.isSaveButtonDisabled = function () {
        return $scope.isOutsideProvider()
            || $scope.isLoading.btn.eSign
            || !$scope.hasPrescribeItEnabled
            || !$scope.eRxSettings.enabled
            || !$scope.eRxSettings.provider.enabled
            || !$scope.eRxSettings.clinic.id
    }

    function initMultisite() {
        const deferred = $q.defer();
        // generate sites if multisites enabled
        rxService.getBooleanProperty("multisites").then(function(result) {
            $scope.isMultisite = result;
            if (!$scope.isMultisite) {
                deferred.resolve($scope.siteId);
                return;
            }
            rxService.getProviderSites($scope.loggedInProvider.providerNo).then(function success(results) {
                if (results) {
                    $scope.providerSites = results.sort(rxService.sortBySiteName);
                    if ($scope.appointmentNo) {
                        rxService.getDefaultLocation($scope.appointmentNo).then(function(result) {
                            $scope.siteId = (result && locationInSites(result))
                                ? result
                                : null;
                            deferred.resolve($scope.siteId);
                        });
                    } else {
                        $scope.siteId = null;
                        deferred.resolve($scope.siteId);
                    }
                }
            });
            rxService.getSites().then(function (results) {
                $scope.allSites = results.sort(rxService.sortBySiteName);;
            });
        });
        return deferred.promise;
    }

    $scope.init = function() {
        $scope.indivicareDeployment = $window.__env.indivicareDeployment;
        const appointmentNoParameter = $location.search().appointmentNo;
        $scope.appointmentNo = appointmentNoParameter === "null" ? null : appointmentNoParameter;
        $scope.disableWarningLabel = JSON.parse(localStorage.getItem('disableWarningLabel')) || false;
        if ($state.current.name === 'rx') {
            $scope.changeState('rx.prescription', $state.params);
        }
        authenticationService.getLoggedInInfo().then(async function (result) {
            $scope.loggedInProvider = result.data;

            providerFavourites();

            let pharmacyPromises = [
                rxService.getAllPharmacies(),
                propertyService.findByNameAndRequestProvider("default_pharmacy")
            ];
            $q.all(pharmacyPromises).then(function (results) {
                const pharmacies = results != null && results[0].length > 0 ? results[0] : null;
                const defaultPharmacyPreference = results != null && results[1].length > 0 ? results[1][0] : null;
                if (pharmacies) {
                    for (const p of pharmacies) {
                        $scope.pharmacies.push({pharmacyInfo: p, preferredOrder: null, preferred: false});
                        if (p.serviceLocationIdentifier === PILLWAY_ID) {
                            $scope.pillwayPharmacy = p;
                        }
                        if (p.id.toString() === defaultPharmacyPreference?.value) {
                            $scope.prefPharmacies.push({pharmacyInfo: p, preferredOrder: null, preferred: true});
                            $scope.prefPharmaciesOptions.push({option: p.displayName ? p.displayName : p.name, value: p.id, obj: p});
                            $scope.selectedPharmacy = p;
                        }
                    }
                }
            });

            rxService.getAllDemographicPharmacies($state.params.demographicNo).then(function (results2) {
                if (results2) {
                    angular.forEach(results2, function (res) {
                        $scope.allPharmacies.push({pharmacyInfo: res, preferredOrder: null, preferred: false});
                        $scope.prefPharmacies.push({pharmacyInfo: res, preferredOrder: null, preferred: true});
                        $scope.prefPharmaciesOptions.push({option: res.displayName ? res.displayName : res.name, value: res.id, obj: res});
                    });

                    if ($scope.prefPharmacies.length > 0) {
                        $scope.selectedPharmacy = $scope.prefPharmacies[0].pharmacyInfo;
                    } else {
                        $scope.selectedPharmacy = null;
                    }
                    $scope.prefPharmaciesOptions.push({option: 'None', value: '', obj: null});
                }
            });

            // functions ahead depend on multisite
            await initMultisite();
            updateERxProperties();
            demographicInit();
        });
    };

    let updateERxProperties = function () {
        let promises = [];
        promises.push(rxService.getRxProperties($scope.siteId));
        promises.push(constantsService.getERxFormularyJurisdictions());

        $q.all(promises).then(function (data) {
            // Prepare the Erx Properties
            $scope.rxProperties = data[0];
            $scope.rxProperties.siteId = $scope.siteId;
            $scope.lab.show = $scope.rxProperties.rx_show_lab === 'true';
            $scope.showPillwayButton = $scope.rxProperties.rx_show_pillway_button === 'true' || !angular.isDefined($scope.rxProperties.rx_show_pillway_button);
            $scope.showDispenseInternally = $scope.rxProperties.rx_enable_internal_dispensing === 'true';

            $scope.indivicareLinkEnabled = $scope.indivicareDeployment
                && $scope.rxProperties.indivicareLinkEnabled === 'true';

            $scope.eRxSettings = {
                enabled: $scope.rxProperties.erx_enabled === 'true',
                service: $scope.rxProperties.erx_service,
                clinic: {
                    applicationId: $scope.rxProperties.erx_clinic_application_id,
                    id: $scope.rxProperties.erx_clinic_id
                },
                provider: {
                    enabled: $scope.rxProperties.erx_provider_enabled === 'true',
                    id: $scope.rxProperties.erx_provider_id
                },
                vendor: {
                    name: $scope.rxProperties.erx_vendor,
                    software: $scope.rxProperties.erx_vendor_software,
                    version: $scope.rxProperties.erx_vendor_version
                },
                webservice: {
                    host: $scope.rxProperties.erx_host,
                    path: $scope.rxProperties.erx_path
                },
                formulary: {
                    url: $scope.rxProperties.erx_formulary_url,
                    jurisdiction: $scope.rxProperties.erx_formulary_jurisdiction
                }
            };
            $scope.checkERxConfig();

            // Set the Formulary Jurisdictions
            $scope.eRxFormularyJurisdictions = data[1];
            // Updates the selected jurisdiction
            updateSelectedJurisdiction();
        });
    }

    $scope.isCurrent = function(item) {
        var isCurrent = false;
        if (item) {
            var today = new Date().setHours(0, 0, 0, 0);
            var endDate = new Date(item.endDate);
            endDate = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate(), 23, 59, 59);
            isCurrent = endDate.getTime() > today;
        }
        return isCurrent;
    };

    $scope.isExpireInReference = function (item) {
        var isExpireInReference = false;
        if (item) {
            var today = new Date();
            var endDate = new Date(item.endDate);
            var isCurrent = $scope.isCurrent(item);
            var month = 1000 * 60 * 60 * 24 * 30;

            isExpireInReference = !item.longTerm && (isCurrent && (endDate.getTime() - today.getTime()) <= month);
        }

        return isExpireInReference;
    };

    $scope.listPharmacies = function() {
        for (var prefPharm in $scope.prefPharmacies) {
            var pharmacy = $filter('filter')($scope.demographic.preferredPharmacies, {pharmacyInfo: {id: $scope.prefPharmacies[prefPharm].pharmacyInfo.id}}, true)[0];
            if (pharmacy) {
                $scope.prefPharmacies[prefPharm].preferredOrder = pharmacy.preferredOrder;
            }
        }

        for (prefPharm in $scope.prefPharmacies) {
            var pharmIndex = -1;
            for (var pharm in $scope.pharmacies) {
                if (angular.equals($scope.prefPharmacies[prefPharm].pharmacyInfo.id, $scope.pharmacies[pharm].pharmacyInfo.id)) {
                    pharmIndex = pharm;
                    break;
                }
            }

            if (pharmIndex !== -1) {
                $scope.pharmacies.splice(pharmIndex, 1);
            }
        }
    };

    $scope.setAllergyStartDate = function(date) {
        $scope.editAllergy.startDate = date;
    }

    $scope.setRxDate = function(date, itemIndex) {
        $scope.rx.items[itemIndex].rxDate = date;
    }

    $scope.setMainWrittenDate = function (date) {
        $scope.mainWrittenDate = date;
        $scope.rx.items.forEach((item) => {
            item.writtenDate = date;
        });
    }

    $scope.setSelectedPharmacy = function(pharmacy) {
        $scope.selectedPharmacy = pharmacy;
    };

    $scope.setWrittenDate = function(date, itemIndex) {
        $scope.rx.items[itemIndex].writtenDate = date;
    }

    $scope.openWindow = function (destination) {
        var url = '';
        var windowName = '_blank';
        var windowFeatures = '';
        const appointmentNo = $location.search().appointmentNo;

        if (destination === 'eChart') {
            url = `/${__env.oscarContext}/oscarEncounter/IncomingEncounter.do?&demographicNo=` + $scope.demographic.demographicNumber;
            windowFeatures = 'height=710,width=1024';
        } else if (destination === 'HCV') {
            url = '/CardSwipe/?hc=' + $scope.demographic.hin + ' ' + $scope.demographic.ver + '&providerNo=' + $scope.loggedInProvider.providerNo;
            windowFeatures = "height=500,width=500"
        } else if (destination === 'Inbox') {
            url = `/${__env.appContext}/app/components/inbox/`;
            windowFeatures = 'height=710,width=1024';
        } else if (destination === 'master') {
            url = `/${__env.oscarContext}/demographic/demographiccontrol.jsp?demographic_no=` + $scope.demographic.demographicNumber + '&displaymode=edit' + ( appointmentNo ? `&appointment=${appointmentNo}` : '');
            windowFeatures = 'height=710,width=1024';
        }

        $window.open(url, windowName, windowFeatures);
    };

    $scope.removeDrug = function (index) {
        $scope.rxItemHide(index);
        $scope.rx.items.splice(index, 1);
        $scope.setFormularyQueryItemState(index)
        resetDrugSearch();
        queryDrugInteractions();
    };

    let getCprIdByPrescriptionNumber = function (prescriptionNumber) {
        let deferred = $q.defer();
        rxService.getPrescriptionByScriptNo(prescriptionNumber).then(function success (results) {
            let site = $filter('filter')($scope.providerSites, {siteId: results.siteId})[0];
            if (site && site.cprId) {
                deferred.resolve(site.cprId);
            } else {
                deferred.resolve(null);
            }
        }, function error () {
            deferred.reject("No cprId linked with prescription number.");
        });
        return deferred.promise;
    }

    $scope.sendClinicianCommunication = async function(item) {
        // LinkedPatient and RemoteDrug currently only used for IC4 Link
        let linkedPatient = $scope.indivicareLinkEnabled
            && $scope.demographic.cachedLinkDemographicState === 'LINKED';
        event.stopPropagation();
        let cprId = $scope.isMultisite
            ? await getCprIdByPrescriptionNumber(item.scriptNo)
            : null;
        $window.open(
            '/' + __env.appContext + '/app/components/messages/?compose=erx'
            + '&demographicNo=' + $scope.demographic.demographicNumber
            + '&topic=' + item.id
            + (item.destinationPharmacy != null && item.destinationPharmacy.id != null
                ? '&to=' + item.destinationPharmacy.id : '')
            + '&remoteDrug=' + (item.remoteDrug && linkedPatient)
            + (cprId ? ('&cprId=' + cprId) : ''),
            "msg",
            'height=600,width=1024');
    };


    $rootScope.sendClinicianCommunicationRx = function(prescription) {
            event.stopPropagation();

            $window.open(`/${__env.appContext}/app/components/messages/?compose=erx&demographicNo=` + $scope.demographic.demographicNumber + '&topicRequisition=' + prescription.scriptNo + (prescription.destinationPharmacy != null && prescription.destinationPharmacy.id != null ? '&to=' + prescription.destinationPharmacy.id : ""), "msg", 'height=600,width=1024');
      };

    $scope.getRenewalsByDrugId = function(drugId)
    {
        rxService.getRenewalByDrugId(drugId).then(function (renewals) {
            let modalInstance = $uibModal.open({
                templateUrl: "../messages/renewalRequestInformationModal.html",
                controller: 'renewalRequestModalController',
                size: 'xl',
                windowClass: 'manage-pharmacies-modal',
                resolve: {
                    renewals: function () {
                        return renewals;
                    }
                }
            });
        });
    };

    $scope.addAllergyByName = function (allergyName) {
        if (allergyName !== 'NKDA') {
            rxService.searchAllergens(allergyName).then(function (data) {
                if (data != null) {
                    rxService.getAllergyFromDrugDatabase(data[0].id).then(function(data) {
                        if (data != null) {
                            data.demographicNo = $scope.demographic.demographicNumber;
                            data.providerNo = $scope.loggedInProvider.providerNo;
                            $scope.setEditAllergy(data, data.typeCode);
                            $('#modifyAllergy').modal('show');
                        }
                    });
                }
            });
        } else {
            $scope.allergy.description = 'NKDA';
            $scope.setEditAllergy($scope.allergy, 0);
        }
    };

    $scope.setEditAllergy = function (allergy, typeCode) {
        $scope.editAllergy = null;
        $scope.showDescInput = false;
        if (allergy) {
            $scope.openModifyAllergyModal();
            $scope.showDescInput = !allergy.id || allergy.description == null || allergy.description.trim().length === 0
            $scope.editAllergy = angular.copy(allergy);
            $scope.editAllergy.typeCode = typeCode;
        }
    };


    $scope.setFilter = function(filter) {
        $scope.selectedFilter = filter;
    };

    $scope.setFormularyQueryItemState = function(itemIndex, state = '') {
        let fqItemStateObj = $filter('filter')($scope.formularyQueryItemStates, {itemIndex: itemIndex}, true)[0];

        if (fqItemStateObj != null) {
            $scope.formularyQueryItemStates[$scope.formularyQueryItemStates.indexOf(fqItemStateObj)].state = state;
        } else {
            $scope.formularyQueryItemStates.push({itemIndex: itemIndex, state: state});
        }
    }

    $scope.setIsCustomDrugSelected = function(val) {
        $scope.isCustomDrugSelected = val != null ? val : $scope.isCustomDrugSelected;
    }

    $scope.setPatientIdType = function(idType, drug) {
        drug.controlledSubstanceInfo.patientIdentificationType = idType;
    };

    $scope.searchAllergens = function(keyword) {
        let options = [];
        if (keyword != null && keyword.length > 2) {
            return rxService.searchAllergens(keyword).then(function (data) {

                if (data != null) {
                    angular.forEach(data, function (res) {
                        options.push({option: res.name, value: res.id});
                    });
                }
                $scope.typeAheadAllergy = options;
                return data;
            });
        } else {
            $scope.typeAheadAllergy = options
        }
    };
    $scope.getAllergyFromDrugDatabase = function(selectedAllergy) {
        let allergyId = angular.isDefined(selectedAllergy.id) ? selectedAllergy.id : selectedAllergy;

        return rxService.getAllergyFromDrugDatabase(allergyId).then(function(data) {
            if (data != null) {
                data.demographicNo = $scope.demographic.demographicNumber;
                data.providerNo = $scope.loggedInProvider.providerNo;
                $scope.setEditAllergy(data, data.typeCode);
                $('#modifyAllergy').modal('show');
            }
        });
    };

    $scope.saveAllergy = function (allergy, changeArchiveStatus = false) {

        if (allergy.description == null || allergy.description.trim().length === 0) {
            // make sure description (appears as name field) is filled
            let msg = "Allergy name required.";
            if (changeArchiveStatus) {
                msg += " Please modify before updating the status."
            }
            alertify.error(msg);
        } else {
            if (changeArchiveStatus) {
                allergy.archived = !allergy.archived;
            }

            if(allergy.typeCode === 0 && !allergy.archived){
                quickModal.confirm("This allergy entry is considered custom and will not provide any drug reaction warnings" +
                    "<br>Are you sure you want to continue?", function(e){
                        if(e) {
                            rxService.saveAllergy(allergy).then(function (result) {
                                if (result && result.id) {
                                    $scope.setEditAllergy();
                                    alertify.success("Allergy saved.");

                                    let matchingAllergy = $filter('filter')($scope.demographicAllergies, {id: result.id}, true)[0];
                                    if (matchingAllergy != null) {
                                        // replace
                                        $scope.demographicAllergies.splice($scope.demographicAllergies.indexOf(matchingAllergy), 1, result);
                                    } else {
                                        // add
                                        $scope.demographicAllergies.push(result);
                                    }

                                    if (angular.isDefined($uibModalStack.getTop().value.modalDomEl) && $uibModalStack.getTop().value.modalDomEl.hasClass('modify-allergies-modal')) {
                                        $scope.closeModal();
                                    }
                                }
                            }).catch(function (errorData) {
                                alertify.error("Error saving allergy.");
                            });
                        }
                    },
                    {title: "Warning"});
            } else {
                rxService.saveAllergy(allergy).then(function (result) {
                    if (result && result.id) {
                        $scope.setEditAllergy();
                        alertify.success("Allergy saved.");

                        let matchingAllergy = $filter('filter')($scope.demographicAllergies, {id: result.id}, true)[0];
                        if (matchingAllergy != null) {
                            // replace
                            $scope.demographicAllergies.splice($scope.demographicAllergies.indexOf(matchingAllergy), 1, result);
                        } else {
                            // add
                            $scope.demographicAllergies.push(result);
                        }

                        if (angular.isDefined($uibModalStack.getTop().value.modalDomEl) && $uibModalStack.getTop().value.modalDomEl.hasClass('modify-allergies-modal')) {
                            $scope.closeModal();
                        }
                    }
                }).catch(function (errorData) {
                    alertify.error("Error saving allergy.");
                });
            }
        }
    };

    $scope.searchDrugs = function(keyword, itemIndex) {
        var rxItem = $scope.rx.items[itemIndex];
        if (rxItem != null && !rxItem.isCustom && rxItem.brandName == null) {
            return rxService.searchDrugs(keyword).then(function (data) {
                return data;
            });
        }
    };

    $scope.showExpandAllLimitedUseCodes = function (drug) {
        let id = $scope.rx.items.indexOf(drug);
        for (let i = 0; i < drug.limitedUseCodeList.length; i++) {
            let luCodeId = drug.limitedUseCodeList[i].reasonForUseId;
            if (luCodeId !== null) {
                let node = document.getElementById("luCode_" + id + "_" + luCodeId);
                let spanHeight = node.offsetHeight;
                let lineHeight = parseInt(window.getComputedStyle(node, null).getPropertyValue('line-height'));
                let lines = Math.ceil(spanHeight / lineHeight);
                if (lines > 3 || node.scrollHeight > node.clientHeight) {
                    return true;
                }
            }
        }
        return false;
    };

    $scope.typeAheadSearch = function(val) {
        $scope.typeAheadRx.input = val;
        if ($scope.isRxSearchActive) {
            $scope.lastRxSearch = val;
        } else {
            if (val.length > 2 && !$scope.isCustomDrugSelected) {
                $scope.isRxSearchActive = true;
                rxService.searchDrugs(val).then(function (data) {
                    let options = [];
                    if (data != null) {
                        angular.forEach(data, function (res) {
                            options.push({option: res.name, value: res.id, tag: res.lookUpCodeFound ? ' (LU)' : ''});
                        });
                    }
                    $scope.typeAheadRx.options = options;
                    $scope.isRxSearchActive = false;
                    if ($scope.lastRxSearch !== '') {
                        let newVal = $scope.lastRxSearch;
                        $scope.lastRxSearch = '';
                        if (newVal !== val) {
                            $scope.typeAheadSearch(newVal);
                        }
                    }
                });
            } else {
                $scope.typeAheadRx.options = [];
            }
        }
    };

    $scope.mirrorInputHandler = function($event) {
        // update field value for other drugs
        if ($scope.rxOverride && $scope.rxOverrideFieldName != null) {
            let newVal = $event.currentTarget.value;
            if (newVal.includes('string:')) {
                // strip 'string:' prefix from value in cases where it's added (eg select inputs using ng-value)
                newVal = newVal.substring(newVal.indexOf(':') + 1);
            }
            // get drug field name
            let field = $scope.rxOverrideFieldName.replace('drug.', '');
            // set new value to desired field on all rx items
            $scope.rx.items.forEach(item => item[field] = newVal);
        }
        $scope.$apply();
    }

    $scope.mirrorInputHandlerBlur = function($event) {
        let inputElement = $event.currentTarget;
        if (inputElement) {
            let fieldToMirror = inputElement.getAttribute('ng-model');
            // un-highlight fields
            document.querySelectorAll(`[ng-model="${fieldToMirror}"]`)
                .forEach(element => element.parentElement.classList.remove('editing-override-selected'));
        }
        $scope.$apply();
    }

    $scope.onSelectEditableDrugField = function() {
        let editType = $scope.rxOverride ? 'edit-silent' : 'edit';
        let editableTagList = document.getElementsByClassName('editable-tag');
        for (let i = 0; i < editableTagList.length; i++) {
            editableTagList[i].classList.remove(editType);
        }

        let editableTagDiv = event.currentTarget;
        editableTagDiv.classList.add(editType);

        let inputElement = editableTagDiv.querySelector('[ng-model]');
        if ($scope.rxOverride && inputElement) {
            // get inner element bound to drug model
            let fieldToMirror = inputElement.getAttribute('ng-model');
            $scope.rxOverrideFieldName = fieldToMirror;

            // highlight fields
            document.querySelectorAll(`[ng-model="${fieldToMirror}"]`)
                .forEach(element => element.parentElement.classList.add('editing-override-selected'));
            inputElement.focus();

            // create and add handlers that mirror fields
            inputElement.addEventListener('change', $scope.mirrorInputHandler);
            inputElement.addEventListener('blur', $scope.mirrorInputHandlerBlur);
        }
    };

    $scope.getFromDrugDatabase = function(selectedDrug) {
        let drugId = angular.isDefined(selectedDrug.id) ? selectedDrug.id : selectedDrug;

        return rxService.getFromDrugDatabase(drugId).then(function (data) {
            if (data) {
                $scope.addDrug(data);
            }
        });
    };

    $scope.parseInstructions = function(itemIndex) {
        event.stopPropagation();
        if ($scope.rx.items[itemIndex] != null && $scope.rx.items[itemIndex].special && $scope.rx.items[itemIndex].parseInstructions) {
            let rxItem = angular.copy($scope.rx.items[itemIndex]);
            // if custom, set the name to the drug object's customName field so it is preserved when parsing instructions
            rxItem.customName = rxItem.isCustom ? rxItem.name : null;

            let formularyResults = rxItem.formularyResults;
            let allergyWarnings = rxItem.allergyWarnings;
            let interactionWarnings = rxItem.interactionWarnings;
            let warningMessages = rxItem.warningMessages;
            return rxService.parseInstructions(rxItem).then(function (data) {
                rxItem = data;
                rxItem.formularyResults = formularyResults;
                rxItem.allergyWarnings = allergyWarnings;
                rxItem.interactionWarnings = interactionWarnings;
                rxItem.warningMessages = warningMessages;

                // update item
                angular.copy(rxItem, $scope.rx.items[itemIndex]);
            });
        }
    };

    $scope.disableParseInstructions = function(drug) {
        drug.parseInstructions = false;
    }

    $scope.parseInstructionsText = function(drug) {
        return 'Parse Instructions - ' + (drug.parseInstructions ? 'Enabled' : 'Disabled');
    }

    $scope.toggleControlledSubstance = function(drug) {
        if (drug.controlledSubstanceInfo == null) {
            drug.controlledSubstanceInfo = angular.copy($scope.controlledSubstanceInfo);
        } else {
            $scope.controlledSubstanceInfo = angular.copy(drug.controlledSubstanceInfo);
            drug.controlledSubstanceInfo = null;
        }
    };

    $scope.rxItemHasWarnings = function(item) {
        if (item != null && item.allergyWarnings != null && item.interactionWarnings != null) {
            return item.allergyWarnings.length > 0 || item.interactionWarnings.length > 0;
        }
        return false;
    }

    $scope.rxOverrideAll = function() {
        $scope.rxOverride = !$scope.rxOverride;
        if ($scope.rxOverride) {
            $scope.enableRxOverride();
        } else {
            $scope.disableRxOverride();
        }
    }

    $scope.enableRxOverride = function() {
        // Open all rx items
        $scope.rxItemOpen = $scope.rx.items.map((item, index) => index);

        // expand all rx fields
        let editableTagList = document.getElementsByClassName('editable-tag');
        for (let i = 0; i < editableTagList.length; i++) {
            editableTagList[i].classList.add('editing-override');
        }
    }
    $scope.disableRxOverride = function() {
        $scope.rxOverride = false;
        let editableTagList = document.getElementsByClassName('editable-tag');
        for (let i = 0; i < editableTagList.length; i++) {
            editableTagList[i].classList.remove('editing-override');
        }
    }


    $scope.rxItemHide = function(index) {
        // prevent parent show event from firing
        event.stopPropagation();
        if ($scope.rxItemOpen.indexOf(index) >= 0) {
            $scope.rxItemOpen.splice($scope.rxItemOpen.indexOf(index), 1);
        }
    }

    $scope.rxItemShow = function(index) {
        if ($scope.rxItemOpen.indexOf(index) < 0) {
            $scope.rxItemOpen.push(index);
        }
    }

    $scope.saveDrug = function(item) {
        if (item && !item.archived) {
            item.longTerm = !item.longTerm
            rxService.saveDrug(item).then(function (result){
                if (result && result.id) {
                    alertify.success("Drug  " + (item.id ? 'updated' : 'saved') + "!");
                }
            }).catch(function(errorData){
                alertify.error("Error " + (item.id ? 'updating' : 'saving') + "  drug");
            });
        }
    };

    $scope.getDrugSyncStatus = function (item)
    {
    	if(item) {
    		let isAvailableForPatientPortal = item.isAvailable || (item.autoSyncDate && new Date(item.autoSyncDate) <= new Date());
    		if(!item.lastSyncedDate && !item.isAvailable && (item.autoSyncDate && new Date(item.autoSyncDate) > new Date())) {
    			return "Pending";
    		} else if (item.lastSyncedDate && isAvailableForPatientPortal) {
    			return "Read";
    		} else if (!item.lastSyncedDate && (isAvailableForPatientPortal || item.autoSyncDate)) {
    			return "Available";
    		} else if (!item.lastSyncedDate  && !isAvailableForPatientPortal) {
    			return "";
    		} else if (item.lastSyncedDate && !isAvailableForPatientPortal) {
    			return "Removed";
    		}
    	}
 },
    $scope.saveFavourite = function (favourite, action) {
        if (action === 'delete') {
            quickModal.confirm("Are you sure you wish to delete this favourite?", function (e) {
                if (e) {
                    rxService.deleteFavourite(favourite).then(function (result){
                        if (result && result.id) {
                            favourite.deleted = true;
                            alertify.success("Favourite deleted");
                            providerFavourites();
                        }
                    }).catch(function(errorData){
                        alertify.error("Error deleting favourite.");
                    });
                }
            });
        } else if (favourite instanceof Array) {
            rxService.saveFavourites(favourite).then(function (results){
                if (results) {
                    alertify.success("Favourites " + (action === 'copy' ? 'copied' : 'saved') + ".");
                    providerFavourites();
                }
            }).catch(function(errorData){
                alertify.error("Error " + (action === 'copy' ? 'copying' : 'saving') + " favourites.");
            });
        } else {
            rxService.saveFavourite(favourite).then(function (result){
                if (result && result.id) {
                    alertify.success("Favourite " + (action === 'copy' ? 'copied' : 'saved') + ".");
                    providerFavourites();
                }
            }).catch(function(errorData){
                alertify.error("Error " + (action === 'copy' ? 'copying' : 'saving') + " favourite.");
            });
        }
    };

    $scope.showArchiveOptions = function(item) {
        $scope.drugToArchive = item;
    };

    $scope.sortAllergies = function (sortBy) {

        if (!sortBy || sortBy.length <= 0) {
            sortBy = ['archived', 'description'];
        } else {
            sortBy = [sortBy];
            if (sortBy[0] !== 'description') {
                sortBy.push('description');
            }
        }

        $scope.allergyFilters.reverseOrder = ($scope.allergyFilters.orderBy[0] === sortBy[0]) ? !$scope.allergyFilters.reverseOrder : false;
        $scope.allergyFilters.orderBy = sortBy;
        $scope.demographicAllergies = $filter('orderBy')($scope.demographicAllergies, $scope.allergyFilters.orderBy, $scope.allergyFilters.reverseOrder);
    };

    $scope.pushAllergiesToPortal = function () {
    	dataSharingService.pushAllergiesToPortal($scope.demographic.demographicNumber).then(function (data) {
    		if (data) {
    			alertify.success("Allergies shared!");
    			$scope.init();

    		} else {
    			alertify.error("Error sharing allergies");
    		}
    	});

    };

    $scope.validatePrescriptionFields = function (drug, index, ePrescribe) {
        $scope.processing = {
            show: true,
            message: 'Validating . . .'
        };
        $scope.errorMessageContext["drugName" + index] = drug.name;
        if (typeof(drug.quantity) === "string") {
            drug.quantity = drug.quantity.trim();
        }
        let validPrescriptionItem = true;
        let tempErrorMessage = "<p><b> {{ ctrl.drugName" + index
            + " }}:</b></p><ul>";

        if (drug.name.trim().length === 0) {
            tempErrorMessage += "<li>Drug name cannot be empty</li>";
            $scope.validPrescription = false;
            validPrescriptionItem = false;
        }
        if (drug.name != null && drug.name.trim().length > 1000) {
            tempErrorMessage += "<li>Drug name cannot exceed 1000 characters</li>";
            $scope.validPrescription = false;
            validPrescriptionItem = false;
        }
        if (drug.method != null && drug.method !== '' && drug.method.length > 40) {
            tempErrorMessage += "<li>Drug method cannot exceed 40 characters</li>";
            $scope.validPrescription = false;
            validPrescriptionItem = false;
        }
        if (drug.route != null && drug.route !== '' && drug.route.length > 120) {
            tempErrorMessage += "<li>Drug route cannot exceed 120 characters</li>";
            $scope.validPrescription = false;
            validPrescriptionItem = false;
        }
        if (typeof drug.rxDate === 'undefined' || drug.rxDate == null) {
            tempErrorMessage += "<li>Invalid Start Date</li>";
            $scope.validPrescription = false;
            validPrescriptionItem = false;
        }
        if (typeof drug.writtenDate === 'undefined') {
            tempErrorMessage += "<li>Invalid Written Date</li>";
            $scope.validPrescription = false;
            validPrescriptionItem = false;
        }

        if (typeof drug.takeMin === 'undefined' || isNaN(drug.takeMin)) {
            tempErrorMessage += "<li>Invalid Minimum Value</li>";
            $scope.validPrescription = false;
            validPrescriptionItem = false;
        }
        if (typeof drug.takeMax === 'undefined' || isNaN(drug.takeMax)) {
            tempErrorMessage += "<li>Invalid Maximum Value</li>";
            $scope.validPrescription = false;
            validPrescriptionItem = false;
        }
        if (typeof drug.takeMax !== 'undefined' && typeof drug.takeMin !== 'undefined'
                && parseInt(drug.takeMin) > parseInt(drug.takeMax)) {
            tempErrorMessage += "<li>Maximum Value cannot be less than Minimum Value</li>";
            $scope.validPrescription = false;
            validPrescriptionItem = false;
        }
        if (typeof drug.duration === 'undefined' || isNaN(drug.duration) || isNaN(drug.takeMin)) {
            tempErrorMessage += "<li>Invalid Duration Value</li>";
            $scope.validPrescription = false;
            validPrescriptionItem = false;
        }
        if (typeof drug.quantity === 'undefined' || isNaN(drug.quantity) || isNaN(drug.takeMin)) {
            tempErrorMessage += "<li>Invalid Quantity</li>";
            $scope.validPrescription = false;
            validPrescriptionItem = false;
        }
        if (ePrescribe && ((drug.quantity === '' || drug.quantity == null || drug.quantity == '0') && (drug.duration === '' || drug.duration == null || drug.duration == '0'))) {
            tempErrorMessage += "<li>Must enter a Quantity or Duration Value</li>";
            $scope.validPrescription = false;
            validPrescriptionItem = false;
        }
        if (ePrescribe && (drug.quantity !== '' && drug.quantity !== null) && (drug.quantityUnit === '' || drug.quantityUnit == null)) {
            tempErrorMessage += "<li>Must enter a Quantity Unit</li>";
            $scope.validPrescription = false;
            validPrescriptionItem = false;
        }
        if (typeof drug.repeat === 'undefined' || isNaN(drug.repeat)) {
            tempErrorMessage += "<li>Invalid Repeat Value</li>";
            $scope.validPrescription = false;
            validPrescriptionItem = false;
        } else if (drug.repeat !== '' && drug.repeat > 125) {
            tempErrorMessage += "<li>Repeats value cannot exceed 125</li>";
            $scope.validPrescription = false;
            validPrescriptionItem = false;
        }
        // Checks if the duration is not null, not empty, and not equal to 0, and if the duration unit is null or empty
        if (drug.duration && drug.duration !== '0' && !drug.durationUnit) {
            tempErrorMessage += "<li>Duration requires Duration Unit</li>";
            $scope.validPrescription = false;
            validPrescriptionItem = false;
        }
        if (!drug.duration && drug.durationUnit) {
            tempErrorMessage += "<li>Duration Unit requires Duration</li>";
            $scope.validPrescription = false;
            validPrescriptionItem = false;
        }

        if (drug.rxDate < drug.writtenDate
                && $scope.compareDateWithoutTime(drug.rxDate, drug.writtenDate) < 0) {
            tempErrorMessage += "<li>Rx Valid Date Cannot Be Before Written Date</li>";
            $scope.validPrescription = false;
            validPrescriptionItem = false;
        }

        if (drug.controlledSubstanceInfo && !drug.controlledSubstanceInfo.patientIdentificationNumber) {
            tempErrorMessage += "<li>Prescribing a controlled substance requires a valid patient ID</li>";
            $scope.validPrescription = false;
            validPrescriptionItem = false;
        }

        if (!validPrescriptionItem) {
            $scope.errorMessage += tempErrorMessage + "</ul>";
        }
    };

    $scope.compareDateWithoutTime = function (dateA, dateB) {
        const a = new Date(dateA).setHours(0, 0, 0, 0);
        const b = new Date(dateB).setHours(0, 0, 0, 0);
        return a > b ? 1 : a < b ? -1 : 0;
    }

    $scope.validateQuantityFields = function(drug) {
        // in order to calculate the total quantity, convert all date values to a common type eg. days
        let frequencyInDays = null;
        let matchedFrequency = $filter('filter')($scope.frequencies, {unit: drug.freqCode});
        if (matchedFrequency.length > 0) {
            if (drug.takeMax != 0) {
                frequencyInDays = matchedFrequency[0].frequencyInDays * drug.takeMax;
            } else {
                frequencyInDays = matchedFrequency[0].frequencyInDays;
            }
        }

        //Ensure duration and duration units are set to blank if no duration is specified
        if(drug.duration === "0"){
            drug.duration = '';
            drug.durationUnit = '';
        }

        let durationInDays = null;

        let matchedDuration = $filter('filter')($scope.durations, {unit: drug.durationUnit});
        if (matchedDuration.length > 0) {
            durationInDays = matchedDuration[0].durationInDays * drug.duration;
        }
        let calculatedQuantity = Math.ceil(frequencyInDays * durationInDays);

        let enteredQuantity = drug.quantity;
        if (enteredQuantity != calculatedQuantity) {
            return 'Warning! The entered quantity does not match the calculated quantity (' + calculatedQuantity + ')';
        }

        return null;
    }

    $scope.homeDelivery = function(openPrintModalAfterSave = false) {
        rxService.setPreferredPharmacy($scope.pillwayPharmacy.id, $scope.demographic.demographicNumber, 0);
        if ($scope.prefPharmaciesOptions.find(o => o.value === $scope.pillwayPharmacy.id) === undefined) {
            $scope.prefPharmaciesOptions.unshift({
                option: $scope.pillwayPharmacy.name,
                value: $scope.pillwayPharmacy.id,
                obj: $scope.pillwayPharmacy
            });
        }
        $scope.setSelectedPharmacy($scope.pillwayPharmacy);
        $scope.savePrescription(openPrintModalAfterSave);
    };

    $scope.homeDeliveryHelp = function () {
        console.log('help me');
        let modalInstance = $uibModal.open({
            templateUrl: "pillwayModal.html",
            scope: $scope,
            size: 'sm',
        });
    };

    $scope.isPillwayEnabled = function () {
      return $scope.pillwayPharmacy != null && ($scope.showPillwayButton || $scope.pillwayButtonEnabled);
    };

    $scope.savePrescription = function(openPrintModalAfterSave = false) {
        $scope.rx.siteId = $scope.siteId;
        $scope.isLoading.btn.save = true;
        $scope.validPrescription = true;
        $scope.errorMessage = "Please correct the following fields to proceed: <br/><br/>";
        $scope.errorMessageContext = {title: "Error"};
        angular.forEach($scope.rx.items, function (drug, index) {
            if (drug.isCustom) {
                setCustomDrugData(drug);
            }
            $scope.validatePrescriptionFields(drug, index, false);
            if (!drug.outsideProviderCheckbox) {
                drug.outsideProviderName = "";
                drug.outsideProviderOhip = "";
            }
        });
        if ($scope.validPrescription) {
            $scope.rx.providerNo = $scope.providerNo;
            $scope.rx.demographic = $scope.demographic;
            $scope.rx.demographicNo = $scope.demographicNo;

            $scope.processing = {show: true, message: 'Saving . . .'};
            rxService.savePrescription($scope.rx).then(function (data) {
                if ($scope.indivicareLinkEnabled
                    && $scope.demographic.cachedLinkDemographicState === 'LINKED') {
                    rxService.saveRemotePrescription(data).then(function (response) {
                        if (response && response.data) {
                            response.data.scriptNo = response.data.id;
                            $scope.finalizePrescription(response.data, openPrintModalAfterSave);
                        } else {
                            alertify.error(response);
                        }
                    }).catch(function () {
                        alertify.error('Failed to save remote prescription.');
                    });
                } else {
                    $scope.finalizePrescription(data, openPrintModalAfterSave);
                }
            }).catch(function (errorData) {
                $scope.processing = {show: false, message: ''};
                alertify.error("Error saving prescription");
            }).finally(function () {
                $scope.isLoading.btn.save = false;
            });
        } else {
            $scope.isLoading.btn.save = false;
            $scope.processing = {show: false, message: ''};
            quickModal.popUp($scope.errorMessage, $scope.errorMessageContext);
        }
    };

    async function rePrescribeForEditing(data) {
        for (const drug of data.items) {
           await rxService.getRePrescribeDrug(drug.id).then(function (newDrug) {
                $scope.rePrescribeDrug(newDrug);
            });
        }
    }

    $scope.finalizePrescription = function (data, openPrintModalAfterSave) {
        if (data && data.scriptNo) {
            alertify.success("Prescription saved!");
            $scope.clearRx();
            if (openPrintModalAfterSave) {
                rePrescribeForEditing(data);
                $scope.openPrintModal(data.scriptNo, false);
            } else {
                $scope.getDemographicItems();
            }
        } else {
            alertify.error("Error saving prescription");
        }
        $scope.processing = {show: false, message: ''};
    }

    $scope.isPasteToEmrDefault = function()  {
        return $scope.rxProperties
            ? $scope.rxProperties.rx_paste_to_encounter_default === 'true'
            : false;
    }

    $scope.rxTokenIsValid = function() {
        return $scope.rxProperties.isTokenSet
            && $scope.rxProperties.isTokenSet !== 'false'
            && parseInt($scope.rxProperties.tokenExpiry) >= new Date().getTime()
            && $scope.tokenSiteId === $scope.siteId;
    }

    $scope.saveErxPrescription = function(pasteIntoEMR) {
        $scope.isLoading.btn.eSign = true;
        let openPrintModalAfterSave = !canEPrescribe();

        if (!$scope.rxTokenIsValid()) {
            $scope.generateSAMLSession($scope.siteId).then(function () {
                $scope.saveErxPrescription();
            }).catch(function (error) {
                if (error) {
                    alertify.error(error);
                }
            });
        } else {
            $scope.validPrescription = true;
            $scope.errorMessage = "Please correct the following fields to proceed: <br/>";
            $scope.errorMessageContext = {title: "Error"};
            angular.forEach($scope.rx.items, function (drug, index) {
                $scope.validatePrescriptionFields(drug, index, true);
                if (drug.isCustom) {
                    setCustomDrugData(drug);
                }
                if (!drug.outsideProviderCheckbox) {
                    drug.outsideProviderName = "";
                    drug.outsideProviderOhip = "";
                }
            });
            if ($scope.validPrescription) {
                $scope.rx.providerNo = $scope.providerNo;
                $scope.rx.demographic = $scope.demographic;
                $scope.rx.siteId = $scope.siteId;
                $scope.processing = {show: true, message: 'Saving and Sending to PrescribeIT . . .'};
                let rxTask = 'NEW';
                let organizationId = '';

                if (canEPrescribe()) {
                    organizationId = $scope.selectedPharmacy.organizationId;
                } else {
                    rxTask = 'DEFERRED';
                }

                $scope.sendErxPrescription($scope.rx, rxTask, organizationId, openPrintModalAfterSave, pasteIntoEMR);
            } else {
                $scope.isLoading.btn.eSign = false;
                $scope.processing = {show: false, message: ''};
                quickModal.popUp($scope.errorMessage,  $scope.errorMessageContext);
            }
        }
    };
    $scope.resendErxPrescription = function(rxItem) {
        // get a list of the drugs in the prescription
        let prescriptionDrugsInScope = $scope.medicationList.filter(drug => drug.scriptNo == rxItem.scriptNo)
        // set the drugs processing ressend
        angular.forEach(prescriptionDrugsInScope, function(drug){ drug.showProcessingResend = true; });
        rxService.getPrescriptionByScriptNo(rxItem.scriptNo).then(function (prescription) {
            // Set task and destination org from the prescription
            var rxTask = 'NEW';
            let destinationOrganizationId = '';
            if (prescription.destinationPharmacy != null && prescription.destinationPharmacy.organizationId != null) {
                destinationOrganizationId = prescription.destinationPharmacy.organizationId;
            } else {
                rxTask = 'DEFERRED';
            }

            if (prescription.items.length > 1) {
                // Confirm to the user the multiple drugs are part of this prescription
                let confirmMessage = 'This drug is part of a prescription that contains multiple items:<br/>';
                let confirmMessageContext = {};
                angular.forEach(prescription.items, function(drug, index){
                    // Add drug summary to message
                    confirmMessageContext["drugSpecial"+index] = drug.special;
                    confirmMessage += '{{ ctrl.drugSpecial' + index + ' }}<br/>';
                });
                confirmMessage += 'Are you sure you would like to resend all these items?';
                quickModal.confirm(confirmMessage, function (userConfirmed) {
                    if (userConfirmed) {
                        $scope.sendErxPrescription(prescription, rxTask, destinationOrganizationId);
                    } else {
                        // set the drugs processing ressend
                        angular.forEach(prescriptionDrugsInScope, function(drug){ drug.showProcessingResend = false; });
                    }
                },
                confirmMessageContext);
            } else {
                $scope.sendErxPrescription(prescription, rxTask, destinationOrganizationId);
            }
        });
    }
    $scope.sendErxPrescription = function(prescription,
                                          rxTask,
                                          organizationId,
                                          openPrintModalAfterSave = false,
                                          pasteIntoEmr = false) {
        if (!$scope.rxTokenIsValid()) {
            $scope.generateSAMLSession($scope.siteId).then(function () {
                angular.forEach($scope.medicationList.filter(drug => drug.scriptNo == prescription.scriptNo), drug => drug.showProcessingResend = false);
                $scope.sendErxPrescription(prescription, rxTask, organizationId, openPrintModalAfterSave);
                return false;
            }).catch(function (error) {
                if (error) {
                    alertify.error(error);
                }
                return false;
            });
        }
        return rxService.saveAndSendErxPrescription(prescription, rxTask, organizationId, $scope.siteId).then(function (result) {
            if ($scope.indivicareLinkEnabled
                && $scope.demographic.cachedLinkDemographicState === 'LINKED') {
                prescription.demographicNo = $scope.demographic.demographicNumber;
                prescription.providerNo = $scope.loggedInProvider.providerNo;
                prescription.scriptNo = result.scriptNo;
                rxService.saveRemotePrescription(prescription).then(function (response) {
                    if (response && response.data) {
                        $scope.finalizeErxPrescription(result, openPrintModalAfterSave, pasteIntoEmr);
                    } else {
                        alertify.error(response);
                    }
                });
            } else {
                $scope.finalizeErxPrescription(result, openPrintModalAfterSave, pasteIntoEmr);
            }
        }).catch(function (errorData) {
            $scope.isLoading.btn.eSign = false;
            alertify.error("Error sending to PrescribeIT");
        });
    }

    $scope.finalizeErxPrescription = function (result, openPrintModalAfterSave, pasteIntoEMR = false) {
        $scope.getDemographicItems(); // refresh drug list
        if (rxUtil.isValidResult(result)) {
            $scope.clearRx();
            let successMessage= "Sent to PrescribeIT";
            if (result.isDeferred) {
                successMessage = successMessage + " as Deferred Transmission";
            }
            alertify.success(successMessage);
            // open print modal with prescription if deferred is selected
            if (openPrintModalAfterSave && result.isDeferred) {
                $scope.openPrintModal(result.scriptNo, false);
            }
            if (pasteIntoEMR) {
                // Add updated drug special text to prescription
                let note = eChartService.prepareNote($scope.rx,
                    $scope.loggedInProvider.formattedName,
                    $scope.rxProperties, true, result.drugSpecials);
                eChartService.pasteIntoEChart(note, $scope.demographic.demographicNumber);
            }
        }

        $scope.isLoading.btn.eSign = false;
    }

    $scope.openPrintModal = function (openOnScriptNo, isReprint = true) {
        var modalInstance = $uibModal.open({
            templateUrl: "prescriptionPrintModal.html",
            controller: 'prescriptionPrintModalController',
            size: 'xl',
            windowClass: 'prescription-print-modal',
            resolve: {
                demographic: function () {
                    return $scope.demographic;
                },
                appointmentNo: function () {
                    return $scope.appointmentNo;
                },
                rxProperties: function () {
                    return $scope.rxProperties;
                },
                openOnScriptNo: function () {
                    return openOnScriptNo;
                },
                selectedPharmacy: function () {
                    return $scope.selectedPharmacy;
                },
                siteId: function () {
                    return $scope.siteId;
                },
                pharmacies: function () {
                    return $scope.prefPharmaciesOptions;
                },
                isReprint: function () {
                    return isReprint;
                },
                mainWrittenDate: function () {
                    return $scope.mainWrittenDate;
                },
                loggedInProvider: function () {
                    return $scope.loggedInProvider;
                }
            }
        });
    };

    $scope.openDHDR = function() {
        rxService.isSessionValid().then(function(result) {
            if (result) {
                popupEHRService(`/${__env.oscarContext}/dhdr/?demographic_no=` + $scope.demographic.demographicNumber);
            } else {
                popupEHRService(`/${__env.appContext}/#/one-id/login?forward=/${__env.oscarContext}/dhdr/`
                    + encodeURIComponent(`?demographic_no=` + $scope.demographic.demographicNumber));
            }
        }).catch(function(error) {
            alertify.error('There was an error checking your ONE ID Session. Please try again.');
            console.log(error);
        });
    }

    function popupEHRService(url) {
        let popup = $window.open(url, 'EHR Service', 'height=' + screen.height + ',width=' + screen.width + '');
        let windowCheck = $interval(function () {
            if (popup.closed) {
                rxService.patientClose($scope.demographic.demographicNumber).then(function(response) {
                    console.log(response);
                }).catch(function(error) {
                    alert('There was an error closing the patient in the context.');
                    console.log(error);
                })
                $interval.cancel(windowCheck);
            }
        }, 1000);
    }


    $scope.openManageFavouritesModal = function() {
        let modalInstance = $uibModal.open({
            templateUrl: "favourite/manageFavourites.html",
            controller: 'manageFavouriteCtrl',
            controllerAs: 'MFC',
            size: 'xl',
            windowClass: 'manage-favourites-modal',
            resolve: {
                categoryNameComparator: function () {
                  return $scope.categoryNameComparator
                },
                loggedInProviderNo: function() {
                    return $scope.loggedInProvider.providerNo;
                },
                frequencies: function() {
                    return $scope.frequencies;
                },
                durations: function() {
                    return $scope.durations;
                },
                popOutEditableTag: function() {
                    return $scope.onSelectEditableDrugField;
                },
                favouriteCategories: function() {
                    return $scope.favouriteCategories;
                },
                validateQuantityFields: function () {
                    return $scope.validateQuantityFields
                }
            }
        });

        modalInstance.result.then(function() {
            providerFavourites();
        });
    };

    $scope.openManageAllergiesModal = function() {
        let modalInstance = $uibModal.open({
            templateUrl: "manageAllergies.html",
            windowClass: 'manage-allergies-modal no-scroll',
            controller: 'manageAllergiesCtrl',
            scope: $scope,
            resolve: {
                demographicNo: function () {
                    return $scope.demographic.demographicNumber;
                },
                lifeStage: function () {
                    return $scope.lifeStage;
                },
                onsetOfReaction: function () {
                    return $scope.onsetOfReaction;
                },
                severityOfReaction: function () {
                    return $scope.severityOfReaction;
                }
            }
        });
    };


    $scope.openModifyAllergyModal = function() {
        let modalInstance = $uibModal.open({
            templateUrl: "modifyAllergy.html",
            windowClass: 'modify-allergies-modal',
            scope: $scope,
            resolve: {
                demographicNo: function () {
                    return $scope.demographic.demographicNumber;
                }
            }
        });
    };

    $scope.openCopyFavouritesModal = function() {
        let modalInstance = $uibModal.open({
            templateUrl: "copyFavourites.html",
            controller: 'copyFavouriteCtrl',
            controllerAs: 'CFC',
            size: 'xl',
            windowClass: 'copy-favourites-modal',
            resolve: {
                loggedInProviderNo: function() {
                    return $scope.loggedInProvider.providerNo;
                },
                frequencies: function() {
                    return $scope.frequencies;
                },
                durations: function() {
                    return $scope.durations;
                },
                popOutEditableTag: function() {
                    return $scope.onSelectEditableDrugField;
                }
            }
        });

        modalInstance.result.then(function (updateFavourites) {
            if (updateFavourites) {
                providerFavourites();
            }
        });
    };


    $scope.closeModal = function () {
        $uibModalStack.dismiss($uibModalStack.getTop().key);
    };

    $scope.closeModalAll = function () {
        $uibModalStack.dismissAll();
    };

    let locationInSites = function (siteId) {
        return !!$filter('filter')($scope.providerSites, {siteId: siteId})[0];
    }

    let getReauthenticationPrompt = function () {
        return 'Successfully re-authenticated for current location!';
    }

    $scope.generateSAMLSession = function(siteId) {
        let promise = new Promise((resolve, reject) => {
            authenticationService.initiateRequestOTP(siteId).then(function(result) {
                $scope.isLoading.btn.eSign = false;
                if (result === 'ALREADY_AUTHENTICATED') {
                    $scope.rxProperties.isTokenSet = true;
                    $scope.tokenSiteId = siteId;
                    authenticationService.getSAMLExpiry(siteId).then(function (result){
                        if (result !== null){
                            $scope.rxProperties.tokenExpiry = result.data;
                        } else {
                            $scope.rxProperties.tokenExpiry = 0;
                        }
                    });
                    resolve();
                } else if (result === 'REQUEST_SENT') { //*
                    quickModal.prompt('Please enter the 5-Digit One-Time Password you received via Text Message to re-authenticate with PrescribeIT: ', function(evt, value) {
                        if (evt) {
                            authenticationService.requestSAMLToken(value, siteId).then(function(SAMLResult) {
                                if (SAMLResult != null && SAMLResult.data !== '' && SAMLResult.data === 200) {
                                    alertify.success(getReauthenticationPrompt());
                                    $scope.rxProperties.isTokenSet = true;
                                    $scope.tokenSiteId = siteId;
                                    authenticationService.getSAMLExpiry(siteId).then(function (result){
                                        if (result !== null){
                                            $scope.rxProperties.tokenExpiry = result.data;
                                        }else{
                                            $scope.rxProperties.tokenExpiry = 0;
                                        }
                                    });
                                    reject();
                                } else {
                                    $scope.rxProperties.isTokenSet = false;
                                    $scope.rxProperties.tokenExpiry = 0;
                                    reject('Something went wrong while contacting PrescribeIT. Please ensure all of your account credentials are correct.');
                                }
                            });
                        } else {
                            reject('OTP Entry Cancelled.');
                        }
                    });
                } else {
                   reject('Something went wrong while contacting PrescribeIT. Please ensure all of your account credentials are correct.');
                }
            });
        });
        return promise;
    };

    $scope.makeExistingDrugCustom = function(drug) {
        if (!drug.isCustom && drug.brandName != null) {
            quickModal.confirm('If you change the drug name and write your own drug, you will lose the following functionality:'
                + '<br/>  *  Known Dosage Forms / Routes'
                + '<br/>  *  Drug Allergy Information'
                + '<br/>  *  Drug-Drug Interaction Information'
                + '<br/>  *  Drug Information'
                + '<br/><br/>Are you sure you wish to use this feature?',
                function (e) {
                    if (e) {
                        setCustomDrugData(drug);
                    } else {
                        drug.name = drug.brandName;
                    }
                });
        }
    };

    $scope.makeNewDrugCustom = function() {
        quickModal.confirm('This feature will allow you to manually enter a prescription.'
            + '<br/>Warning: you will lose the following functionality:'
            + '<br/>  *  Quantity and Repeats'
            + '<br/>  *  Known Dosage Forms / Routes'
            + '<br/>  *  Drug Allergy Information'
            + '<br/>  *  Drug-Drug Interaction Information'
            + '<br/>  *  Drug Information'
            + '<br/><br/>Are you sure you wish to use this feature?',
            function (e) {
                if (e) {
                    resetDrug();
                    let drug = angular.copy($scope.drug);
                    setCustomDrugData(drug);
                    $scope.isCustomDrugSelected = true;
                    $scope.addDrug(drug);
                } else {
                    resetDrug();
                    $scope.isCustomDrugSelected = false;
                }
            },
            {title: "Warning"});
    };

    $scope.openInstructionExamples = function(itemIndex, drug) {
        rxService.listPreviousInstructions(drug).then(function(response){
            $scope.examples = response;

            fillInstructionsAndClose = function(text) {
                $scope.rx.items[itemIndex].special = text;
                $scope.parseInstructions(itemIndex);
                $scope.closeModal();
                document.getElementById("dosage-instructions-input").value = text;
            }

            fillPharmacyInstructionsAndClose = function(text) {
                $scope.rx.items[itemIndex].pharmacyInstructions = text;
                $scope.closeModal();
                document.getElementById("pharmacy-instructions-input").value = text;
            }

            createExampleString = function() {
                let str = "";
                $scope.examples.forEach(example =>
                    str += "<tr class='example-row'>"
                        + "<td><a onclick='fillInstructionsAndClose(\""
                        + example["instruction"] + "\")'>"
                        + example["instruction"] + "</a></td>"
                        + "<td><a onclick='fillPharmacyInstructionsAndClose(\""
                        + example["pharmacy_instruction"] + "\")'>"
                        + example["pharmacy_instruction"] + "</a></td>"
                        + "</tr>");
                return str;
            }

            createInstructionExamplesTable = function() {
                const example = createExampleString();
                return '<table class="example-table"><tr class=\'example-row\'>'
                    + '<th class=\'example-header\' colSpan="2">'
                    + $scope.rx.items[itemIndex].brandName
                    + " Rx Examples"
                    + "</th></tr>"
                    + "<tr class='instruction-row'>"
                    + "<td>Dosage Instructions</td>"
                    + "<td>Pharmacy Instructions</td>"
                    + "</tr>"
                    + example
                    + "</table>";
            }

            quickModal.popUp(createInstructionExamplesTable());
        })
    };

    var setCustomDrugData = function(drug) {
        drug.id = null;
        drug.isCustom = true;
        drug.gcnSeqNo = 0;
        drug.name = drug.name != null && drug.name.trim().length > 0 ? drug.name : $scope.typeAheadRx.input
        drug.customName = drug.name;
        drug.brandName = null;
        drug.genericName = null;
        drug.regionalIdentifier = null;
        drug.allergyWarnings = [];
        drug.interactionWarnings = [];
        drug.warningMessages = [];
    };

    $scope.showCategory = function (category) {
        var showCategory = true;
        var isSearching = $scope.favouriteKeyword && $scope.favouriteKeyword.length > 0;

        // If the category id is 0, there is not a search, and there are no favourites OR there is a search
        // but there are no visible anchors (no favourites matching the filter), hide the category
        if (category.id === 0 && !isSearching && (!category.favourites || category.favourites.length === 0)) {
            showCategory = false;
        } else if (isSearching && $('#category_' + category.id + ' a').not('.ng-hide').not('.ignore').length === 0){
            showCategory = false;
        }
        return showCategory;
    };

    var demographicInit = function() {
        demographicService.getDemographic($state.params.demographicNo).then(function(result) {
            $scope.demographic = result.data;
            resetDrugSearch();

            demographicService.getDemographicExtensions($scope.demographic.demographicNumber, ['opt_out_prescribeit']).then(function (results) {
                $scope.hasPrescribeItEnabled = results.data == null
                    || !('opt_out_prescribeit' in results.data)
                    || results.data['opt_out_prescribeit'] !== 'true';
            });

            demographicService.getDemographicLabs($scope.demographic.demographicNumber).then(function(results) {
                if (results) {
                    $scope.demographic.labs = results;
                    $scope.getMostRecentLab();
                }
            });

            let getAllergiesForDemographic =
                rxService.getDemographicAllergies(
                    $scope.demographic.demographicNumber)
                .then(function(results) {
                    return results;
                });

            let getRxItemsForDemographic =
                rxService.getRxItemsForDemographic(
                    $scope.demographic.demographicNumber)
                .then(function (results) {
                    return results;
                });

            let getRemoteDrugs = null;
            let getRemoteAllergies = null;

            if ($scope.indivicareLinkEnabled
                && $scope.demographic.cachedLinkDemographicState === 'LINKED') {
                getRemoteDrugs = rxService.getRemoteDrugs(
                    $scope.demographic.demographicNumber
                ).then(function (response) {
                    return $scope.parseRemoteDrugDates(response);
                }).catch(function () {
                    alertify.error('Error retrieving remote drugs');
                });
                getRemoteAllergies = rxService.getDemographicLinkAllergies(
                    $scope.demographic.demographicNumber
                ).then(function (response) {
                    return $scope.parseRemoteAllergyProperties(response);
                }).catch(function () {
                    alertify.error('Error retrieving remote allergies');
                });
            }

            $q.all([
                getRxItemsForDemographic,
                getRemoteDrugs,
                getAllergiesForDemographic,
                getRemoteAllergies])
            .then(function (results) {
                $scope.medicationList = results[0] ? results[0] : [];
                if (results[1]) {
                    $scope.medicationList = $scope.medicationList.concat(results[1]);
                }

                $scope.demographicAllergies = results[2] ? results[2] : [];
                if (results[3]) {
                    $scope.demographicAllergies = $scope.demographicAllergies.concat(results[3]);
                }

                $scope.recentMedicationDates =
                    getRecentlyUpdatedMedication($scope.medicationList);

                if ($state.params.ltm) {
                    $scope.reRxAllLongTerm();
                    const urlParams = window.location.href.substring(window.location.href.lastIndexOf('?'));
                    history.replaceState({}, '', urlParams.replace("&ltm=true", ""));
                }
                cloneMedicationListWithAttributes();
                $scope.isLoading.section.itemList = false;
            });

            $scope.allergy.demographicNo = $scope.demographic.demographicNumber;
            $scope.allergy.providerNo = $scope.loggedInProvider.providerNo;

            $scope.rx.demographic = $scope.demographic;
            $scope.rx.loggedInProvider = $scope.loggedInProvider.providerNo;

            constantsService.getControlledSubstanceDemographicIdTypes().then(function (data) {
                angular.forEach(data, function (d) {
                    $scope.controlledSubstanceDemographicIdTypes.push({value: d, option: d});
                });

                $scope.controlledSubstanceInfo = {
                    patientIdentificationType: $scope.controlledSubstanceDemographicIdTypes[0].value,
                    patientIdentificationNumber: $scope.demographic.hin,
                    providerPractitionerNumber: $scope.loggedInProvider.practitionerNo
                };
            });

            $scope.listPharmacies();

            if ($state.params.openAllergies) {
                $scope.openManageAllergiesModal();
            }

            if ($state.params.openPrescription && $state.params.rx) {
                $scope.openPrintModal($state.params.rx, true);
            }

            if ($state.params.pharmaList) {
                $scope.openManagePharmaciesModal();
            }
        }).catch(function(errorData) {
            console.log(errorData);
        }).finally(function () {
            $scope.isLoading.page = false;
        });
        rxService.getDrugInteractionWarnings($state.params.demographicNo, null).then(function (interactionResults) {
            $scope.interactionWarnings = interactionResults;
        }).catch(function(errorData) {
            console.log(errorData);
        });
        rxService.getPatientAllergyWarnings(
            $state.params.demographicNo,
            null,
            $scope.demographicAllergies
        ).then(function (allergyWarnings) {
            $scope.allergyWarnings = allergyWarnings;
        }).catch(function (errorData){
            console.log(errorData);
        });
    };

    $scope.categoryNameComparator = function(category1, category2) {
        let result = 0;
        if (category1.id === 0 || category2.id === 0) {
            // Uncategorized will always be last
            result = category1.id > category2.id ? -1 : 1;
        } else if (category1.name !== category2.name) {
            result = category1.name.toLowerCase() < category2.name.toLowerCase() ? -1 : 1;
        }

        return result;
    }

    var providerFavourites = function(){
        providerService.getProviderRxFavourites($scope.loggedInProvider.providerNo).then(function (results) {
            if (results) {
                $scope.favouriteCategories = results.sort((c1,c2)=>$scope.categoryNameComparator(c1,c2));
            }
        });

    };

    var resetDrug = function () {
        $scope.drug = {
            id: '',
            providerNo: $scope.rx.providerNo,
            demographicNo: $scope.rx.demographicNo,
            demographic: $scope.rx.demographic,
            rxDate: new Date(),
            endDate: null,
            writtenDate: new Date(),
            name: null,
            brandName: null,
            gcnSeqNo: 0,
            customName: null,
            takeMin: 0,
            takeMax: 0,
            freqCode: null,
            duration: null,
            durationUnit: null,
            quantity: defaultQuantity,
            dispensingUnits: null,
            repeat: 0,
            lastRefillDate: null,
            noSubs: false,
            prn: false,
            special: defaultInstruction,
            parseInstructions: true,
            archived: false,
            archivedDate: null,
            genericName: null,
            atc: null,
            scriptNo: $scope.rx.scriptNo,
            regionalIdentifier: null,
            unit: null,
            method: null,
            route: null,
            drugForm: null,
            createDate: new Date(),
            dosage: null,
            customInstructions: false,
            unitName: null,
            longTerm: false,
            pastMed: false,
            patientCompliance: null,
            outsideProviderName: null,
            outsideProviderOhip: null,
            outsideProviderCheckbox: false,
            hideFromDrugProfile: null,
            customNote: false,
            isCustom: false,
            pickupDateTime: null,
            eTreatmentType: null,
            rxStatus: null,
            hideFromCpp: false,
            refillDuration: 0,
            refillQuantity: 0,
            dispenseInterval: "0",
            position: 0,
            startDateUnknown: false,
            comment: null,
            lastUpdateDate: null,
            dispenseInternal: false,
            isControlledSubstance: false,
            pharmacyInstructions: defaultInstruction
        };
    };

    let resetDrugSearch = function () {
        $scope.typeAheadRx = {
            input: "",
            results: []
        };
        $scope.isCustomDrugSelected = false;
    }

    var isExtensionActive = function(extensionObj, activeValue) {
        if (extensionObj != null && extensionObj === activeValue) {
            return true;
        }
        return false;
    }

    $scope.getErxSubmitText = function(isDropdown) {
        return rxUtil.getErxSubmitText(isDropdown, this.isPasteToEmrDefault(), canEPrescribe());
    };

    $scope.getTooltip = function() {
        let tooltip;
        if (!$scope.hasPrescribeItEnabled) {
            tooltip = "Disabled due to demographic opting out";
        } else if (!$scope.eRxSettings.enabled || !$scope.eRxSettings.provider.enabled) {
            tooltip = "PrescribeIT disabled";
        } else if ($scope.isMultisite && !$scope.siteId) {
            tooltip = "No location selected";
        } else if (!$scope.eRxSettings.clinic.id) {
            tooltip = "PrescribeIT disabled for current location";
        } else if (canEPrescribe()) {
            tooltip = "ePrescribe to pharmacy";
        } else {
            tooltip = "Save and send as deferred to PrescribeIT";
        }

        return tooltip;
    };

    function canEPrescribe() {
        return $scope.selectedPharmacy != null
            && $scope.selectedPharmacy.organizationId != null
            && $scope.selectedPharmacy.services.find(service => service.concept === "PRESCRIBEIT_RX") !== undefined;
    }

    $scope.isOutsideProvider = function () {
        return $scope.rx.items.some(function(drug){return drug.outsideProviderCheckbox});
    };

    function updateSelectedJurisdiction() {
        let matchingJurisdictions = $scope.eRxFormularyJurisdictions.filter(province => province.provinceCode === $scope.eRxSettings.formulary.jurisdiction);
        if (matchingJurisdictions.length > 0) {
            $scope.selectedJurisdiction = matchingJurisdictions[0];
        }
    }

    $scope.sentViaPrescribeIt = function (medication) {
        return !medication.erxFailedToSend && medication.deliveryMethod === 'ERX';
    };

    $scope.parseRemoteAllergyProperties = function (response) {
        if (response && response.data) {
            let allergies = response.data;
            angular.forEach(allergies, function (allergy) {
                allergy.entryDate = Date.parse(allergy.entryDate);
                allergy.lastUpdateDate = Date.parse(allergy.lastUpdateDate);
                allergy.startDate = Date.parse(allergy.startDate);
                allergy.archived = allergy.archived !== "0";
            });
            return allergies;
        }
        return null;
    }

    $scope.getRemoteDrugs = function (demographicNo) {
        rxService.getRemoteDrugs(
            demographicNo
        ).then(function (response) {
            let drugs = $scope.parseRemoteDrugDates(response);
            $scope.medicationList = $scope.medicationList
                ? $scope.medicationList.concat(drugs)
                : drugs;
        }).catch(function () {
            alertify.error('Error retrieving remote drugs');
        }).finally(function () {
            cloneMedicationListWithAttributes();
        });
    }

    $scope.parseRemoteDrugDates = function (response) {
        if (response && response.data) {
            let drugs = response.data;
            angular.forEach(drugs, function (drug) {
                drug.rxDate = Date.parse(drug.rxDate);
                drug.endDate = Date.parse(drug.endDate);
                drug.writtenDate = Date.parse(drug.writtenDate);
                drug.lastRefillDate = Date.parse(drug.lastRefillDate);
                drug.archivedDate = Date.parse(drug.archivedDate);
                drug.createDate = Date.parse(drug.createDate);
                drug.pickupDateTime = Date.parse(drug.pickupDateTime);
                drug.lastUpdateDate = Date.parse(drug.lastUpdateDate);
            });

            return drugs;
        }
        return null;
    }
})
    .controller('manageAllergiesCtrl', function ($scope, rxService, demographicNo) {
        $scope.typeAheadAllergy = [];

        $scope.searchAllergens = function(keyword) {
            let options = [];
            if (keyword != null && keyword.length > 2) {
                rxService.searchAllergens(keyword).then(function (data) {
                    console.log("searchAllergens");
                    if (data != null) {
                        angular.forEach(data, function (res) {
                            options.push({option: res.name, value: res.id});
                        });
                    }
                    $scope.typeAheadAllergy = options;
                });
            } else {
                $scope.typeAheadAllergy = options
            }
        };

    })

    .controller('prescription', function ($scope, $state, $filter, $rootElement, $rootScope, $location, $window, $q, $uibModal, $uibModalStack, $controller, rxService, appointmentService, authenticationService, demographicService, diseaseRegistryService, providerService, preferenceService, specialistService, constantsService, quickModal) {

        if ($state.params.rx != null) {
            angular.copy($state.params.rx, $scope.rx);
        }
        if ($state.params.reRx != null) {
            $scope.rePrescribe($state.params.reRx);
        }

        $scope.viewMedication = function (item) {
            $scope.changeState('rx.viewMedication', {
                demographicNo: $state.params.demographicNo,
                drug: item,
                rx: $scope.rx
            });
        }
    })

    .controller('rxViewMedication', function ($scope, $state, $stateParams, $filter, $sce, $rootElement, $rootScope, $location, $timeout, $window, $q, $uibModal, $uibModalStack, $controller, rxService, appointmentService, authenticationService, demographicService, diseaseRegistryService, providerService, preferenceService, specialistService, constantsService, quickModal) {
        $scope.medicationDetailList = [];
        $scope.providers = [];
        $scope.isLoading.section.itemList = true;
        $scope.rxRecord = null;
        $scope.isMultisite = false;
        let drug = $state.params.drug;

        let cmn = {};
        $scope.addNote = function (item) {
            event.stopPropagation();
            rxService.getAnnotation($state.params.demographicNo, item.id).then(function (data) {
                cmn = data;
            }).catch(function (error) {
                console.error('Error retrieving drug note');
            }).finally(function () {
                let prompt = cmn.createDate != null ? `Created By: ${cmn.provider != null ? cmn.provider.formattedName : ''} on ${ $filter('date')(cmn.createDate, 'yyyy-MM-dd HH:mm:ss')}<br/>`:'';
                quickModal.prompt(prompt+'Prescriptions Annotation:', function (e, input) {
                        if (e) {
                            rxService.saveAnnotation($state.params.demographicNo, item.id, input).then(function () {
                                alertify.success('Annotation saved')
                            }).catch(function (error) {
                                alertify.error('Error saving annotation');
                                console.error(error);
                            });
                        }
                    }, {
                        promptValue: cmn.note ? cmn.note : '',
                        promptIsTextarea: true,
                        confirmBtnValue: 'Save',
                    }
                );
            });
        }

        $scope.backToRx = function (reRxItem = null) {
            $scope.changeState('rx.prescription', {
                demographicNo: $state.params.demographicNo,
                rx: $state.params.rx,
                reRx: reRxItem
            })
        }

        $scope.getStrengthDisplay = function (item) {
            let unitName = item.unitName != null && item.unitName !== 'null' ? ' ' + item.unitName.trim() : '';
            return item.dosage + unitName;
        }

        $scope.getFormattedPharmacyInfo = function (prescriptionFax) {
            let pharmacyInfo = prescriptionFax != null ? prescriptionFax.pharmacyInfo : null;
            if (pharmacyInfo != null) {
                return $sce.trustAsHtml(`<strong>${pharmacyInfo.name}</strong><br/>` +
                    `${pharmacyInfo.address}<br/>` +
                    (pharmacyInfo.city != null ? `${pharmacyInfo.city}, ` : '') + `${pharmacyInfo.province} ${pharmacyInfo.postalCode}<br/>` +
                    `Phone: ${pharmacyInfo.phone1} <strong>|</strong> Fax: ${pharmacyInfo.fax}<br/>` +
                    `Email: ${pharmacyInfo.email}<br/>`);
            }
            return '';
        }

        $scope.getProviderName = function (m) {
            return m.provider != null ? m.provider.formattedName : '';
        }

        $scope.hasActivePharmacyFilter = function (prescriptionFax) {
            return prescriptionFax != null && prescriptionFax.pharmacyInfo != null && prescriptionFax.pharmacyInfo.status == '1';

        };

        $scope.initViewMedication = async function () {
            $scope.isMultisite = await rxService.getBooleanProperty("multisites");
            let medicationDetailItems = [];

            //Check if the drug is remote, custom or normal
            if (drug.remoteDrug) {
                // drug.remoteDrug added specifically for IC4 Link drugs at the moment
                $scope.medicationDetailList = [drug];
                $scope.isLoading.section.itemList = false;
            } else if (drug.regionalIdentifier == null && drug.customName != null){
                rxService.getCustomDrug($state.params.demographicNo, drug.customName).then(function (data) {
                    medicationDetailItems = data != null ? data : [];
                }).catch(function (error) {
                    console.error('Error retrieving medication information');
                }).finally(function () {
                    $scope.medicationDetailList = medicationDetailItems;
                    $scope.isLoading.section.itemList = false;
                });
            } else {
                rxService.getDrugList(
                    $state.params.demographicNo,
                    drug.regionalIdentifier,
                    drug.customName,
                    drug.brandName
                ).then(function (data) {
                    medicationDetailItems = data != null ? data : [];
                }).catch(function (error) {
                    console.error('Error retrieving medication information');
                }).finally(function () {
                    $scope.medicationDetailList = medicationDetailItems;
                    $scope.isLoading.section.itemList = false;
                });
            }
        }

        $scope.openRxRecord = function (drug) {
            if ($scope.indivicareLinkEnabled && drug.remoteDrug) {
                const data = {
                    drug: drug,
                    drugReasons: [],
                    prescriptionFaxes: []
                }
                $scope.displayRxRecord(data);
            } else {
                rxService.getRxRecord(drug.id).then(function (data) {
                    $scope.displayRxRecord(data);
                }).catch(function (error) {
                    console.error('Error retrieving rx detailed record');
                });
            }
        }

        $scope.displayRxRecord = function (data) {
            $scope.rxRecord = data;
            let modalInstance = $uibModal.open({
                templateUrl: "rxRecord.html",
                scope: $scope,
                size: 'sm',
                windowClass: 'rx-record-modal',
            });

            modalInstance.result.then(function () {
                $scope.rxRecord = null;
            });
        }

        $scope.printRxRecord = function () {
            let printWindow = window.open('', 'PRINT', 'height=700,width=960');
            printWindow.document.write('<html><head><title>Detail Results</title>');
            printWindow.document.write('<link rel="stylesheet" href="' + `/${__env.appContext}/css/bulma/bulma.min.css` + '">');
            printWindow.document.write('<link rel="stylesheet" href="' + `/${__env.appContext}/css/app.css` + '"></head><body>');
            printWindow.document.write('<p class="modal-card-title">Detail Results</p>')
            printWindow.document.write(document.getElementById('rx-detail-record-container').innerHTML);
            printWindow.document.write('</body></html>');

            // IE >= 10
            printWindow.document.close();
            printWindow.focus();

            // allow styles to load
            $timeout(function () {
                printWindow.print();
                printWindow.close();
            }, 500);

            return true;
        }

        if ($scope.indivicareLinkEnabled && drug.remoteDrug) {
            $scope.medicationDetailList = [drug];
            $scope.isLoading.section.itemList = false;
        } else {
            $scope.initViewMedication();
        }
    })
    .filter('durationDescription', function () {
        return function (unit) {
            const durations = {
                D: 'days',
                W: 'weeks',
                M: 'months',
                Y: 'years'
            };

            return durations[unit];
        }
    });