/**
 * 2017 KAI Innovations
 */


angular.module('patientPortalServices', [])
    .service("patientPortalService", function ($http, $q, $log) {
        return {
        	apiPath: `/${__env.appContext}/api/tiahealth`,
            configHeaders: { headers: {"Content-Type": "application/json", "Accept": "application/json"} },
            configHeadersWithCache: { headers: {"Content-Type": "application/json", "Accept": "application/json"}, cache: true },
            createMappingRequest: function (mappingDetails){
                var deferred = $q.defer();
                $http.post(this.apiPath + '/register', mappingDetails, this.configHeaders).then(function success(data) {
                    deferred.resolve(data);
                }, function error(response) {
                    deferred.reject("An error occurred while creating patient portal access request");
                });
                return deferred.promise;
            },
            deletePatientPortalMapping: function (demographicNo, terminationReason){
                var deferred = $q.defer();
                $http.delete(this.apiPath+"/unregister/"+demographicNo + "?terminationReason=" + terminationReason).then(function success(data) {
                deferred.resolve(data);
                }, function error(response) {
                    deferred.reject("An error occurred while removing patient portal access");
                });
                return deferred.promise;
            },
            deleteAllPatientPortalMappings: function (terminationReason){
                var deferred = $q.defer();
                $http.delete(this.apiPath+"/removeAll?terminationReason=" + terminationReason).then(function success(data) {
                    deferred.resolve(data);
                }, function error(response) {
                    deferred.reject("An error occurred while removing patient portal access");
                });
                return deferred.promise;
            },
            
            getPendingRequests: function (demographicNo){
                var deferred = $q.defer();
                $http.get(this.apiPath+"/pending?demographicNo=" + demographicNo).then(function success(data) {
                    deferred.resolve(data);
                }, function error(response) {
                    deferred.reject("An error occurred while fetching pending requests");
                });
                return deferred.promise;
            },
        };
    });
