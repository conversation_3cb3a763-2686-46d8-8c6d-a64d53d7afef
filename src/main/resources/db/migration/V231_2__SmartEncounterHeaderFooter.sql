CALL addColumn('form_smart_encounter', 'headerText', 'text default null');
CALL addColumn('form_smart_encounter', 'footerText', 'text default null');
CALL addColumn('form_smart_encounter', 'headerId', 'int(10) null');
CALL addColumn('form_smart_encounter', 'footerId', 'int(10) null');

CREATE TABLE IF NOT EXISTS SmartEncounterFooter
(
  id int(10) NOT NULL AUTO_INCREMENT,
  name varchar(50) NOT NULL,
  text text NOT NULL,
  htmlPreview varchar(4096) NOT NULL,
  createDate timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  editedDate timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS SmartEncounterHeader
(
  id int(10) NOT NULL AUTO_INCREMENT,
  name varchar(50) NOT NULL,
  text text NOT NULL,
  htmlPreview varchar(4096) NOT NULL,
  createDate timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  editedDate timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
);