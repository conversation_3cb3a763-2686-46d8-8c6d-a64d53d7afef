<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="
			http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
			http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-2.5.xsd
			http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.5.xsd
			http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.5.xsd">

<!-- 
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd
       http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd" default-autowire="no">
 -->

	<!-- configure the available measurement flow sheets -->
	<bean id="measurementTemplateFlowSheet" class="oscar.oscarEncounter.oscarMeasurements.MeasurementTemplateFlowSheetConfig">
		<property name="flowSheets">
			<list>
				<value>classpath:oscar/oscarEncounter/oscarMeasurements/flowsheets/omdDiabetesFlowsheet.xml</value>
				<value>classpath:oscar/oscarEncounter/oscarMeasurements/flowsheets/hypertensionFlowsheet.xml</value>
				<value>classpath:oscar/oscarEncounter/oscarMeasurements/flowsheets/hivFlowsheet.xml</value>
				<value>classpath:oscar/oscarEncounter/oscarMeasurements/flowsheets/intakeFlowsheet.xml</value>
				<value>classpath:oscar/oscarEncounter/oscarMeasurements/flowsheets/financesFlowsheet.xml</value>
				<value>classpath:oscar/oscarEncounter/oscarMeasurements/flowsheets/identificationFlowsheet.xml</value>
				<value>classpath:oscar/oscarEncounter/oscarMeasurements/flowsheets/housingFlowsheet.xml</value>
				<value>classpath:oscar/oscarEncounter/oscarMeasurements/flowsheets/socialLegalFlowsheet.xml</value>
				<value>classpath:oscar/oscarEncounter/oscarMeasurements/flowsheets/diabetesQueensFlowsheet.xml</value>
				<value>classpath:oscar/oscarEncounter/oscarMeasurements/flowsheets/ckdFlowsheet.xml</value>
				<value>classpath:oscar/oscarEncounter/oscarMeasurements/flowsheets/periodicHealthVisit.xml</value>
				<value>classpath:oscar/oscarEncounter/oscarMeasurements/flowsheets/healthTracker.xml</value>
			</list>
		</property>
	</bean>

	<!-- DAO -->
	<bean id="genericIntakeNodeDAO" class="org.oscarehr.PMmodule.dao.GenericIntakeNodeDAO" autowire="byName" />
	<bean id="genericIntakeDAO" class="org.oscarehr.PMmodule.dao.GenericIntakeDAO" autowire="byName" />
	<bean id="healthSafetyDao" class="org.oscarehr.PMmodule.dao.HealthSafetyDao" autowire="byName" />
	<bean id="formsDAO" class="org.oscarehr.PMmodule.dao.FormsDAO" autowire="byName" />


	<!-- managers  -->
	<bean id="issueAdminManager" class="org.caisi.service.IssueAdminManager" autowire="byName" />

	<!-- Generic Intake -->
	<bean id="streetHealthReportManager" class="org.oscarehr.PMmodule.service.impl.StreetHealthReportManagerImpl">
		<property name="demographicDao" ref="demographicDao" />
		<property name="genericIntakeManager" ref="genericIntakeManager" />
	</bean>
	<!-- Generic Intake -->
	<bean id="healthSafetyManager" class="org.oscarehr.PMmodule.service.HealthSafetyManager">
		<property name="healthSafetyDao" ref="healthSafetyDao" />
	</bean>

	<bean id="formsManagerCaisi" class="org.oscarehr.PMmodule.service.impl.FormsManagerImpl" autowire="byName" />
	<bean id="surveyUserManager" class="org.oscarehr.survey.service.impl.UserManagerOscar" autowire="byName" />
	<!-- ========================= SCHEDULED JOBS ========================= -->
	<bean id="scheduledBedProgramDischargeTask" class="org.springframework.scheduling.timer.ScheduledTimerTask">
		<property name="timerTask">
			<bean class="org.oscarehr.PMmodule.task.BedProgramDischargeTask" autowire="byType">
				<property name="programManager" ref="programManager" />
			</bean>
		</property>
		<property name="delay" value="120000" />
		<property name="period" value="3600000" />
	</bean>

	<bean id="scheduledErProgramDischargeTask" class="org.springframework.scheduling.timer.ScheduledTimerTask">
		<property name="timerTask">
			<bean class="org.oscarehr.PMmodule.task.ErProgramDischargeTask">
				<property name="admissionManager" ref="admissionManager" />
				<property name="providerManager" ref="providerManager" />
			</bean>
		</property>
		<property name="delay" value="180000" />
		<property name="period" value="300000" />
	</bean>

	<bean id="scheduledAnonymousClientDischargeTask" class="org.springframework.scheduling.timer.ScheduledTimerTask">
		<property name="timerTask">
			<bean class="org.oscarehr.PMmodule.task.AnonymousClientDischargeTask">
			</bean>
		</property>

		<property name="delay" value="60000" />
		<property name="period" value="3600000" />

	</bean>

	<bean id="scheduledOcanSubmissionTask" class="org.springframework.scheduling.timer.ScheduledTimerTask">
		<property name="timerTask">
			<bean class="oscar.ocan.task.OcanSubmissionTask">
				<property name="genericIntakeManager" ref="genericIntakeManager" />
				<property name="ocanDataProcessor" ref="ocanDataProcessor" />
				<property name="useTestData" value="true" />
				<property name="testDataPath" value="C:/TEMP/ocan_test" />
			</bean>
		</property>
		<property name="delay" value="43200000000" />
		<property name="period" value="86400000" />
	</bean>

	<bean id="ocanIarSubmissionTask" class="org.springframework.scheduling.timer.ScheduledTimerTask">
                <property name="timerTask">
                        <bean class="org.oscarehr.util.OcanIarSubmissionTask">
                        </bean>
                </property>
                <property name="delay" value="43200000000" />
                <property name="period" value="86400000" />
    </bean>

	<bean id="schedulerCaisi" class="org.springframework.scheduling.timer.TimerFactoryBean">
		<property name="scheduledTimerTasks">
			<list>
				<ref bean="scheduledBedProgramDischargeTask" />
				<ref bean="scheduledAnonymousClientDischargeTask" />
				 <ref bean="ocanIarSubmissionTask"/>

				<!--            <ref bean="scheduledErProgramDischargeTask"/> -->
				<!-- ref bean="scheduledOcanSubmissionTask" /-->
			</list>
		</property>
	</bean>
	<bean id="QuestionTypes" class="org.oscarehr.survey.model.QuestionTypes" scope="singleton" />
	<!--
		<bean id="UCRConfigurationManager" class="org.oscarehr.PMmodule.web.reports.custom.UCRConfigurationManager"> <property name="filename"> <value>org/oscarehr/PMmodule/web/reports/custom/test.xml</value> </property> </bean>
	-->


	<!-- DATIS Intake Exporter -->
	<bean id="agencyInformationValidator" class="org.oscarehr.PMmodule.exporter.DATISAgencyInformationValidator" scope="prototype" />
	<bean id="gamingFormValidator" class="org.oscarehr.PMmodule.exporter.DATISGamingFormValidator" scope="prototype" />
	<bean id="listOfProgramsValidator" class="org.oscarehr.PMmodule.exporter.DATISListOfProgramsValidator" scope="prototype" />
	<bean id="mainValidator" class="org.oscarehr.PMmodule.exporter.DATISMainValidator" scope="prototype" />
	<bean id="nonClientServiceValidator" class="org.oscarehr.PMmodule.exporter.DATISNonClientServiceValidator" scope="prototype" />
	<bean id="programInformationValidator" class="org.oscarehr.PMmodule.exporter.DATISProgramInformationValidator" scope="prototype" />

	<bean id="abstractIntakeExporter" abstract="true" class="org.oscarehr.PMmodule.exporter.AbstractIntakeExporter" >
		<property name="genericIntakeManager" ref="genericIntakeManager"></property>
	</bean>
	<bean id="intakeExporterAgencyInformation" class="org.oscarehr.PMmodule.exporter.DATISAgencyInformation" parent="abstractIntakeExporter" scope="prototype">
		<property name="fieldsFile" value="/org/oscarehr/PMmodule/exporter/agencyinformation.xml"></property>
		<property name="validator" ref="agencyInformationValidator"></property>
	</bean>
	<bean id="intakeExporterListOfPrograms" class="org.oscarehr.PMmodule.exporter.DATISListOfPrograms" parent="abstractIntakeExporter" scope="prototype">
		<property name="fieldsFile" value="/org/oscarehr/PMmodule/exporter/listofprograms.xml"></property>
		<property name="validator" ref="listOfProgramsValidator"></property>
	</bean>
	<bean id="intakeExporterMain" class="org.oscarehr.PMmodule.exporter.DATISMain" parent="abstractIntakeExporter" scope="prototype">
		<property name="fieldsFile" value="/org/oscarehr/PMmodule/exporter/main.xml"></property>
		<property name="validator" ref="mainValidator"></property>
	</bean>
	<bean id="intakeExporterProgramInformation" class="org.oscarehr.PMmodule.exporter.DATISProgramInformation" parent="abstractIntakeExporter" scope="prototype">
		<property name="fieldsFile" value="/org/oscarehr/PMmodule/exporter/programinformation.xml"></property>
		<property name="validator" ref="programInformationValidator"></property>
	</bean>
	<bean id="intakeExporterGamblingForm" class="org.oscarehr.PMmodule.exporter.DATISGamingForm" parent="abstractIntakeExporter" scope="prototype">
		<property name="fieldsFile" value="/org/oscarehr/PMmodule/exporter/gamblingform.xml"></property>
		<property name="validator" ref="gamingFormValidator"></property>
	</bean>
	<bean id="intakeExporterNonClientService" class="org.oscarehr.PMmodule.exporter.DATISNonClientService" parent="abstractIntakeExporter" scope="prototype">
		<property name="fieldsFile" value="/org/oscarehr/PMmodule/exporter/nonclientservice.xml"></property>
		<property name="validator" ref="nonClientServiceValidator"></property>
	</bean>

	<context:component-scan base-package="org.oscarehr.PMmodule.dao" />
	<context:component-scan base-package="org.oscarehr.PMmodule.service" />
	<context:component-scan base-package="org.oscarehr.PMmodule.web.admin" />
</beans>
