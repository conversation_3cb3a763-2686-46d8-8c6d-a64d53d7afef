import { HttpClient } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { AuthService } from '@oscar-pro/auth';
import { APP_CONFIG } from '@oscar-pro/config';
import {
  AppConfig,
  InboxId,
  InboxItem,
  InboxItemWAttrs,
  Property,
} from '@oscar-pro/interfaces';
import {
  InboxFilter,
  UrlParamsPipe,
  StripSpecialAttributesPipe,
} from '@oscar-pro/util';
import * as _ from 'lodash';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ApiService } from './api.service';
import { ApiPropertyService } from "./api-property.service";

@Injectable({
  providedIn: 'root',
})
export class ApiInboxService extends ApiService {
  private apiPath: string;

  constructor(
    protected override auth: AuthService,
    protected override http: HttpClient,
    protected override urlParamsPipe: UrlParamsPipe,
    protected apiPropertyService: ApiPropertyService,
    protected stripSpecialAttributesPipe: StripSpecialAttributesPipe,
    @Inject(APP_CONFIG) protected override appConfig: AppConfig
  ) {
    super(auth, http, urlParamsPipe, appConfig);
    this.apiPath = `/${this.appConfig.proContext}/${this.appConfig.apiPrefix}/inbox`;
  }

  getAutocompleteTitle(term: string) {
    const params = { method: 'searchDocumentDescriptions', term };
    const url = this.apiUrl(
      `/${this.appConfig.oscarContext}/dms/ManageDocument.do`,
      params
    );

    return this.http
      .get(url)
      .pipe(catchError((error) => this.handleError(error)));
  }

  getInboxItems(inboxFilter: InboxFilter): Observable<any> {
    const params = _.cloneDeep(inboxFilter.toObject());
    if (params['providerNumber'] === ':all:') {
      params['providerNumber'] = '';
    }

    let url = this.apiUrl(`${this.apiPath}/getInboxItems`, params);
    if (params['demographicNumber']) {
      url = this.apiUrl(`${this.apiPath}/getDemographicInboxItems/${params['demographicNumber']}`, params);
    }
    return this.http
      .get(url)
      .pipe(catchError((error) => this.handleError(error)));
  }

  getInboxItem(labId: InboxId): Observable<InboxItemWAttrs> {
    const url = this.apiUrl(
      `${this.apiPath}/getInboxItem/` + labId.labType + '/' + labId.segmentId,
      {}
    );
    return this.http
      .get<InboxItemWAttrs>(url)
      .pipe(catchError((error) => this.handleError(error)));
  }

  markAsRead(
    providerNo: string,
    labType: string,
    segmentId: number
  ): Observable<null> {
    const params = {
      providerNo: providerNo,
      labType: labType,
      labId: segmentId,
    };
    const url = this.apiUrl(`${this.apiPath}/markAsRead`, params);
    return this.http
      .post(url, {})
      .pipe(catchError((error) => this.handleError(error)));
  }

  getLabType(labId: number): Observable<any> {
    const params = { labId: labId }
    return this.http
      .get(this.apiUrl(`${this.apiPath}/getLabType`, params), {})
      .pipe(catchError( (error => this.handleError(error))));
  }

  fileSelected(
    inboxItems: Array<InboxItemWAttrs>,
    providerNo: string
  ): Observable<any> {
    const params = { providerNo: providerNo };
    const url = this.apiUrl(`${this.apiPath}/fileSelected`, params);
    const items: Array<InboxItem> = this.stripSpecialAttributesPipe.transform(
      _.cloneDeep(inboxItems)
    ) as Array<InboxItem>;

    return this.http
      .post(url, items)
      .pipe(catchError((error) => this.handleError(error)));
  }

  forwardItems(document: any): Observable<any> {
    return this.http
      .post(this.apiUrl(`${this.apiPath}/forwardSelected`, {}), document)
      .pipe(catchError((error) => this.handleError(error)));
  }

  updateOption(optionId: string, value: string): Observable<any> {
    return this.apiPropertyService.updateProperty(optionId, value);
  }
}
