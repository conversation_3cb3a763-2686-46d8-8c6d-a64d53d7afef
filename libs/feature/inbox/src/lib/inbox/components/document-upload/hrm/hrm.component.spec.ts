import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ToastrService} from 'ngx-toastr';
import { HrmComponent } from './hrm.component';

import {
  AddSpecialAttributesPipe,
  ObjectArrayFilterPipe,
  StripSpecialAttributesPipe,
  UrlParamsPipe
} from '@oscar-pro/util';
import { Store } from '@ngrx/store';
import { of } from 'rxjs';
import { HttpClient, HttpHandler } from '@angular/common/http';
import { APP_CONFIG } from '@oscar-pro/config';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('HrmComponent', () => {
  let component: HrmComponent;
  let fixture: ComponentFixture<HrmComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ HrmComponent ],
      providers: [
        AddSpecialAttributesPipe,
        StripSpecialAttributesPipe,
        ObjectArrayFilterPipe,
        {
          provide: Store,
          useValue: {
            pipe: () => of(null),
            dispatch: jest.fn(),
          }
        },
        HttpClient,
        HttpHandler,
        UrlParamsPipe,
        {provide: APP_CONFIG, useValue: {}},
        {provide: ToastrService, useValue: jest.fn()},
      ],
      schemas: [NO_ERRORS_SCHEMA],
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(HrmComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
