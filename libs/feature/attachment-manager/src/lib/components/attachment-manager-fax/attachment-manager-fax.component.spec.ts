import { ComponentFixture, TestBed } from '@angular/core/testing';

import { AttachmentManagerFaxComponent } from './attachment-manager-fax.component';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { By } from "@angular/platform-browser";

describe('AttachmentManagerFaxComponent', () => {
  let component: AttachmentManagerFaxComponent;
  let fixture: ComponentFixture<AttachmentManagerFaxComponent>;

  beforeEach(async () => {
    try {
      await TestBed.configureTestingModule({
        declarations: [AttachmentManagerFaxComponent],
        schemas: [NO_ERRORS_SCHEMA],
      }).compileComponents();
    } catch (e: any) {
      // There's an odd error that occurs when running these tests that occur due importing
      // InputComponent. However, it only occurs the first time the await is ran and seems to
      // work fine when ran again. This is a workaround for that issue.
      if (e.name === 'TypeError' && e.message === "Cannot read properties of undefined (reading 'ngModule')") {
        await TestBed.configureTestingModule({
          declarations: [AttachmentManagerFaxComponent],
          schemas: [NO_ERRORS_SCHEMA],
        }).compileComponents();
      } else {
        throw e;
      }
    }

    fixture = TestBed.createComponent(AttachmentManagerFaxComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should toggle cover page given the toggle widget is clicked', () => {
    expect(component.coverPageState).toBe(false);
    // Show the div class=subject is present
    let subjectComponent = fixture.debugElement.query(By.css('.subject'));
    expect(subjectComponent).toBeFalsy();
    let messageComponent = fixture.debugElement.query(By.css('.message'));
    expect(messageComponent).toBeFalsy();

    component.handleCoverPageToggle(true);
    fixture.detectChanges();

    expect(component.coverPageState).toBe(true);
    subjectComponent = fixture.debugElement.query(By.css('.subject'));
    expect(subjectComponent).toBeTruthy();
    messageComponent = fixture.debugElement.query(By.css('.message'));
    expect(messageComponent).toBeTruthy();

    component.handleCoverPageToggle(false);
    fixture.detectChanges();

    expect(component.coverPageState).toBe(false);
    subjectComponent = fixture.debugElement.query(By.css('.subject'));
    expect(subjectComponent).toBeFalsy();
    messageComponent = fixture.debugElement.query(By.css('.message'));
    expect(messageComponent).toBeFalsy();
  });
});
