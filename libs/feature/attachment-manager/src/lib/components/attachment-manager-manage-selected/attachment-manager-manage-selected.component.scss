.manage-selected {
  width: 45rem;
}

.header {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.title {
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.manage-selected-table {
  grid-template-columns: 1fr 8rem 7.5rem min-content;
}

.content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  padding: var(--spacing-md) var(--spacing-xl);
  background: var(--container-fill);
  height: 100%;
  overflow: auto;
}

.table-header {
  display: contents;
}

.table-row {
  display: contents;

  &:nth-child(odd) weg-table-cell {
    background: var(--container-fill);
  }

  &:nth-child(even) weg-table-cell {
    background: var(--row-fill);
  }
}
