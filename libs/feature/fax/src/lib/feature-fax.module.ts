import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {RouterModule} from '@angular/router';
import {FaxInboxComponent} from "./components/fax-inbox/fax-inbox.component";
import {FaxOutboxComponent} from "./components/fax-outbox/fax-outbox.component";
import { UiModule } from '@oscar-pro/ui';
import {FaxAccountEditComponent} from "./components/fax-config/fax-account-edit/fax-account-edit.component";
import {FaxAccountPageComponent} from "./components/fax-config/fax-account-page/fax-account-page.component";
import { TooltipModule } from 'primeng/tooltip';
import {
  BackdropComponent,
  ColumnComponent,
  TableComponent,
  ButtonComponent,
  DropdownComponent,
  InputComponent,
} from '@oscarpro/well-ui';
import { DatePickerModule } from 'primeng/datepicker';
import { DialogService, DynamicDialogModule } from 'primeng/dynamicdialog';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { PhoneFormatPipe } from '@oscar-pro/util';
import { FaxArchiveDialogComponent } from './components/fax-archive-dialog/fax-archive-dialog.component';

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild([
      {
        path: 'config',
        pathMatch: 'full',
        component: FaxAccountPageComponent,
      },
      {
        path: 'inbox',
        pathMatch: 'full',
        component: FaxInboxComponent,
      },
      {
        path: 'outbox',
        pathMatch: 'full',
        component: FaxOutboxComponent,
      },
    ]),
    UiModule,
    TooltipModule,
    BackdropComponent,
    ColumnComponent,
    ButtonComponent,
    DropdownComponent,
    InputComponent,
    TableComponent,
    DatePickerModule,
    DynamicDialogModule,
    FormsModule,
    ReactiveFormsModule,
    PhoneFormatPipe
  ],
  declarations: [
    FaxAccountPageComponent,
    FaxAccountEditComponent,
    FaxInboxComponent,
    FaxOutboxComponent,
    FaxArchiveDialogComponent,
  ],
  providers: [DialogService],
})
export class FeatureFaxModule {}
