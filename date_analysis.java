import java.time.LocalDate;
import java.time.Month;

class DateAnalysis {
    public static void main(String[] args) {
        System.out.println("=== COMPREHENSIVE DATE ANALYSIS ===");
        System.out.println("Testing when JdbcBillingPageUtilTest fails throughout the year\n");
        
        // Test ALL dates in 2025 to find the exact pattern
        System.out.println("Scanning all dates in 2025 to find failure patterns...\n");

        int failureCount = 0;
        for (int month = 1; month <= 12; month++) {
            for (int day = 1; day <= 31; day++) {
                try {
                    LocalDate testDate = LocalDate.of(2025, month, day);
                    if (wouldTestFail(testDate)) {
                        System.out.println("FAILURE: " + testDate + " (" + testDate.getDayOfWeek() + ")");
                        analyzeDate(testDate);
                        failureCount++;
                    }
                } catch (Exception e) {
                    // Invalid date (e.g., Feb 30), skip
                }
            }
        }

        System.out.println("Total failure dates in 2025: " + failureCount);

        // Also test the specific dates you mentioned
        LocalDate[] specificDates = {
            LocalDate.of(2025, 5, 1),   // May 1 (you mentioned this failed)
            LocalDate.of(2025, 7, 30),  // July 30 (you mentioned this failed)
            LocalDate.of(2025, 7, 31),  // July 31 (today)
        };

        System.out.println("\n=== DETAILED ANALYSIS OF SPECIFIC DATES ===");
        for (LocalDate currentDate : specificDates) {
            analyzeDate(currentDate);
        }
        
        System.out.println("\n=== SUMMARY ===");
        System.out.println("The test fails when the calculated date '5 months X days ago'");
        System.out.println("results in a date that's BEFORE the 6-month cutoff date.");
        System.out.println("This happens due to varying month lengths and Java's month arithmetic.");
    }
    
    static void analyzeDate(LocalDate currentDate) {
        System.out.println("Current Date: " + currentDate + " (" + currentDate.getDayOfWeek() + ")");
        
        // Calculate the 6-month cutoff (what the method uses)
        LocalDate cutoffDate = currentDate.minusMonths(6);
        
        // Calculate the failing test cases
        LocalDate test5m29d = currentDate.minusMonths(5).minusDays(29);
        LocalDate test5m30d = currentDate.minusMonths(5).minusDays(30);
        LocalDate test6m0d = currentDate.minusMonths(6).minusDays(0);
        
        System.out.println("  6-month cutoff: " + cutoffDate);
        System.out.println("  5m29d ago: " + test5m29d + " -> " + getTestResult(test5m29d, cutoffDate));
        System.out.println("  5m30d ago: " + test5m30d + " -> " + getTestResult(test5m30d, cutoffDate));
        System.out.println("  6m0d ago:  " + test6m0d + " -> " + getTestResult(test6m0d, cutoffDate));
        
        // Check if any of the failing test cases would fail
        boolean wouldFail = test5m29d.isBefore(cutoffDate) || 
                           test5m30d.isBefore(cutoffDate) || 
                           test6m0d.isBefore(cutoffDate);
        
        if (wouldFail) {
            System.out.println("  *** TESTS WOULD FAIL ON THIS DATE ***");
        }
        
        System.out.println();
    }
    
    static String getTestResult(LocalDate testDate, LocalDate cutoffDate) {
        boolean isWithinLimit = !testDate.isBefore(cutoffDate);
        return isWithinLimit ? "PASS (true)" : "FAIL (false, expected true)";
    }

    static boolean wouldTestFail(LocalDate currentDate) {
        LocalDate cutoffDate = currentDate.minusMonths(6);
        LocalDate test5m29d = currentDate.minusMonths(5).minusDays(29);
        LocalDate test5m30d = currentDate.minusMonths(5).minusDays(30);
        LocalDate test6m0d = currentDate.minusMonths(6).minusDays(0);

        // Check if any of the test cases that expect 'true' would actually return 'false'
        return test5m29d.isBefore(cutoffDate) || test5m30d.isBefore(cutoffDate);
    }
}
