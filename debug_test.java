import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;

class DebugTest {
    public static void main(String[] args) {
        // Simulate the test case that's failing: 6 months, 0 days ago, expecting true
        LocalDate now = LocalDate.now();
        System.out.println("Current date: " + now);
        
        // Test date calculation (from test)
        LocalDate testDate = now.minusMonths(6).minusDays(0);
        System.out.println("Test date (6 months ago): " + testDate);
        
        // Method calculation
        LocalDate cutoffDate = now.minusMonths(6);
        System.out.println("Cutoff date (6 months ago): " + cutoffDate);
        
        // Check the comparison
        boolean isBefore = testDate.isBefore(cutoffDate);
        boolean result = !isBefore;
        
        System.out.println("testDate.isBefore(cutoffDate): " + isBefore);
        System.out.println("Final result (!isBefore): " + result);
        System.out.println("Expected: true");
        
        // Test the edge case: 5 months 30 days
        LocalDate testDate2 = now.minusMonths(5).minusDays(30);
        System.out.println("\nTest date (5 months 30 days ago): " + testDate2);
        boolean isBefore2 = testDate2.isBefore(cutoffDate);
        boolean result2 = !isBefore2;
        System.out.println("testDate2.isBefore(cutoffDate): " + isBefore2);
        System.out.println("Final result2 (!isBefore): " + result2);
        System.out.println("Expected: true");

        // Let's check what 5 months 29 days would be
        LocalDate testDate3 = now.minusMonths(5).minusDays(29);
        System.out.println("\nTest date (5 months 29 days ago): " + testDate3);
        boolean isBefore3 = testDate3.isBefore(cutoffDate);
        boolean result3 = !isBefore3;
        System.out.println("testDate3.isBefore(cutoffDate): " + isBefore3);
        System.out.println("Final result3 (!isBefore): " + result3);

        // Check days between
        System.out.println("\nDays between cutoff and 5m30d: " + java.time.temporal.ChronoUnit.DAYS.between(testDate2, cutoffDate));
        System.out.println("Days between cutoff and 5m29d: " + java.time.temporal.ChronoUnit.DAYS.between(testDate3, cutoffDate));

        // What would be valid dates within the 6-month limit?
        System.out.println("\n=== Valid dates (should return true) ===");
        LocalDate validDate1 = cutoffDate; // exactly at cutoff
        LocalDate validDate2 = cutoffDate.plusDays(1); // 1 day after cutoff
        LocalDate validDate3 = now.minusMonths(5).minusDays(28); // 5 months 28 days

        System.out.println("Cutoff date: " + cutoffDate + " -> " + !validDate1.isBefore(cutoffDate));
        System.out.println("1 day after cutoff: " + validDate2 + " -> " + !validDate2.isBefore(cutoffDate));
        System.out.println("5 months 28 days ago: " + validDate3 + " -> " + !validDate3.isBefore(cutoffDate));
    }
}
